#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include "src/core/MinimalAgent.h"
#include "src/network/NetworkManager.h"
#include "src/messaging/MessageDispatcher.h"
#include "src/utils/JsonHelper.h"

void TestCommandExecuteHandling() {
    std::cout << "=== Testing command_execute message handling ===" << std::endl;
    
    // Create MinimalAgent instance
    MinimalAgent agent;

    if (!agent.Initialize()) {
        std::cout << "ERROR: Agent initialization failed" << std::endl;
        return;
    }

    std::cout << "SUCCESS: Agent initialized" << std::endl;
    
    // Simulate server command_execute message
    std::string testCommand = R"({
        "id": "cmd_test_123",
        "type": "CameraPlugin",
        "params": {
            "command": "list_cameras",
            "args": {}
        },
        "timestamp": "2025-08-01T14:00:00.000Z"
    })";

    std::cout << "Simulating server command_execute message:" << std::endl;
    std::cout << testCommand << std::endl << std::endl;
    
    // Create NetworkMessage
    NetworkMessage message;
    message.type = "command_execute";
    message.data = testCommand;
    message.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    // Test message processing
    std::cout << "Processing command_execute message..." << std::endl;

    // Get MessageDispatcher and test directly
    auto dispatcher = agent.GetMessageDispatcher();
    if (!dispatcher) {
        std::cout << "ERROR: Cannot get MessageDispatcher" << std::endl;
        return;
    }
    
    std::string result = dispatcher->DispatchMessage(message);

    std::cout << "Processing result:" << std::endl;
    std::cout << result << std::endl << std::endl;

    // Parse result
    picojson::value resultJson;
    if (JsonHelper::Parse(result, resultJson) && JsonHelper::IsObject(resultJson)) {
        auto resultObj = resultJson.get<picojson::object>();

        std::string commandId = JsonHelper::GetString(resultObj, "commandId");
        bool success = JsonHelper::GetBool(resultObj, "success");
        std::string error = JsonHelper::GetString(resultObj, "error");

        std::cout << "Parsed result:" << std::endl;
        std::cout << "  Command ID: " << commandId << std::endl;
        std::cout << "  Success: " << (success ? "true" : "false") << std::endl;

        if (!success && !error.empty()) {
            std::cout << "  Error: " << error << std::endl;
        }

        if (commandId == "cmd_test_123") {
            std::cout << "SUCCESS: Command ID matches correctly" << std::endl;
        } else {
            std::cout << "ERROR: Command ID does not match" << std::endl;
        }
    } else {
        std::cout << "ERROR: Cannot parse result JSON" << std::endl;
    }

    agent.Cleanup();
    std::cout << "SUCCESS: command_execute processing test completed" << std::endl << std::endl;
}

void TestInvalidCommandExecute() {
    std::cout << "=== Testing invalid command_execute message ===" << std::endl;

    MinimalAgent agent;
    if (!agent.Initialize()) {
        std::cout << "ERROR: Agent initialization failed" << std::endl;
        return;
    }

    // Test message missing type field
    std::string invalidCommand = R"({
        "id": "cmd_invalid_123",
        "params": {
            "command": "test_command"
        }
    })";

    NetworkMessage message;
    message.type = "command_execute";
    message.data = invalidCommand;
    message.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    auto dispatcher = agent.GetMessageDispatcher();
    std::string result = dispatcher->DispatchMessage(message);

    std::cout << "Invalid message processing result:" << std::endl;
    std::cout << result << std::endl;

    // Verify error handling
    picojson::value resultJson;
    if (JsonHelper::Parse(result, resultJson) && JsonHelper::IsObject(resultJson)) {
        auto resultObj = resultJson.get<picojson::object>();
        bool success = JsonHelper::GetBool(resultObj, "success");
        std::string error = JsonHelper::GetString(resultObj, "error");

        if (!success && !error.empty()) {
            std::cout << "SUCCESS: Correctly handled error - " << error << std::endl;
        } else {
            std::cout << "ERROR: Error handling incorrect" << std::endl;
        }
    }

    agent.Cleanup();
    std::cout << "SUCCESS: Invalid message test completed" << std::endl << std::endl;
}

int main() {
    std::cout << "Command Execute Processing Test Program" << std::endl;
    std::cout << "=======================================" << std::endl << std::endl;

    TestCommandExecuteHandling();
    TestInvalidCommandExecute();

    std::cout << "All tests completed!" << std::endl;
    return 0;
}
