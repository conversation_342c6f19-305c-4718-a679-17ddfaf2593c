﻿  Building Custom Rule C:/Users/<USER>/Downloads/xeno-rat-main (1)/rat-agent/CMakeLists.txt
cl : 命令行  warning D9002: 忽略未知选项“-g”
cl : 命令行  warning D9002: 忽略未知选项“-O0”
  test_socketio_debug.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\test_socketio_debug.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../test_socketio_debug.cpp”)
  
  WebSocketClient.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/network/WebSocketClient.cpp”)
  
  easywsclient.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(103,42): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(205,27): warning C4244: “参数”: 从“socket_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(209,19): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(230,66): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(497,88): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(499,84): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(502,93): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(504,84): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(505,85): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(507,96): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(509,109): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(510,91): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(511,66): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
  正在生成代码...
  SocketIODebugTest.vcxproj -> C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Release\test_socketio_debug.exe
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
