{"date":"Fri Aug 01 2025 01:02:32 GMT+0800 (中国标准时间)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: listen EADDRINUSE: address already in use :::3000\u001b[39m\n\u001b[31mError: listen EADDRINUSE: address already in use :::3000\u001b[39m\n\u001b[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\u001b[39m\n\u001b[31m    at listenInCluster (node:net:1996:12)\u001b[39m\n\u001b[31m    at Server.listen (node:net:2101:7)\u001b[39m\n\u001b[31m    at startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\dist\\app.js:124:16)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":310072.093},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\dist\\app.js"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":27083,"external":2298599,"heapTotal":35397632,"heapUsed":22494352,"rss":71241728},"pid":11908,"uid":null,"version":"v22.16.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\dist\\app.js:124:16)","timestamp":"2025-08-01 01:02:32:232","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2101,"method":"listen","native":false},{"column":16,"file":"1)\\xeno-rat-server\\dist\\app.js","function":"startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":124,"method":null,"native":false}]}
{"date":"Fri Aug 01 2025 01:31:16 GMT+0800 (中国标准时间)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: listen EADDRINUSE: address already in use :::3000\u001b[39m\n\u001b[31mError: listen EADDRINUSE: address already in use :::3000\u001b[39m\n\u001b[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\u001b[39m\n\u001b[31m    at listenInCluster (node:net:1996:12)\u001b[39m\n\u001b[31m    at Server.listen (node:net:2101:7)\u001b[39m\n\u001b[31m    at startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\app.ts:173:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":311795.89},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":48352,"external":2406177,"heapTotal":36167680,"heapUsed":22713008,"rss":78712832},"pid":9232,"uid":null,"version":"v22.16.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\app.ts:173:12)","timestamp":"2025-08-01 01:31:16:3116","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2101,"method":"listen","native":false},{"column":12,"file":"1)\\xeno-rat-server\\src\\app.ts","function":"startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":173,"method":null,"native":false}]}
{"date":"Fri Aug 01 2025 14:33:36 GMT+0800 (中国标准时间)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: listen EADDRINUSE: address already in use :::3000\u001b[39m\n\u001b[31mError: listen EADDRINUSE: address already in use :::3000\u001b[39m\n\u001b[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\u001b[39m\n\u001b[31m    at listenInCluster (node:net:1996:12)\u001b[39m\n\u001b[31m    at Server.listen (node:net:2101:7)\u001b[39m\n\u001b[31m    at startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\app.ts:173:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":358735.906},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":48352,"external":2406177,"heapTotal":35897344,"heapUsed":22896536,"rss":79015936},"pid":15196,"uid":null,"version":"v22.16.0"},"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\app.ts:173:12)","timestamp":"2025-08-01 14:33:36:3336","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2101,"method":"listen","native":false},{"column":12,"file":"1)\\xeno-rat-server\\src\\app.ts","function":"startServer (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":173,"method":null,"native":false}]}
{"date":"Fri Aug 01 2025 16:20:06 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(496,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(510,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(510,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(515,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(518,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(519,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(527,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(527,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(527,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(553,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(496,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(507,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(510,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(510,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(515,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(518,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(519,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(527,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(527,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(527,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(553,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365124.156},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":101685,"external":2366131,"heapTotal":32227328,"heapUsed":18964312,"rss":60821504},"pid":36328,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(496,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(507,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(507,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(507,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(507,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(508,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(510,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(510,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(513,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(513,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(515,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(518,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(519,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(527,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(527,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(527,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(528,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(528,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(531,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(531,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(552,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(553,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:20:06:206","trace":[{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:20:35 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(517,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(529,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(536,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(539,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(540,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(574,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(517,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(528,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(529,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(536,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(539,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(540,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(574,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365153.75},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":101685,"external":2366131,"heapTotal":32227328,"heapUsed":18854696,"rss":67162112},"pid":31120,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(517,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(528,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(528,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(528,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(528,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(529,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(531,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(531,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(534,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(536,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(539,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(540,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(548,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(548,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(549,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(549,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(552,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(552,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(552,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(573,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(574,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:20:35:2035","trace":[{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:21:04 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(538,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(555,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(555,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(557,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(560,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(561,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(569,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(569,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(569,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(570,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(570,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(595,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(538,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(549,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(552,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(555,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(555,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(557,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(560,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(561,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(569,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(569,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(569,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(570,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(570,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(595,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365182.25},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":101645,"external":2366131,"heapTotal":33275904,"heapUsed":14936824,"rss":67276800},"pid":25384,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(538,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(549,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(549,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(549,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(549,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(550,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(552,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(552,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(555,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(555,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(557,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(560,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(561,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(569,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(569,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(569,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(570,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(570,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(573,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(573,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(573,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(594,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(595,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:21:04:214","trace":[{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:21:32 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(561,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(575,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(575,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(580,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(583,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(584,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(592,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(592,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(592,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(618,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(561,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(573,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(575,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(575,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(580,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(583,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(584,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(592,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(592,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(592,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(618,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365210.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":101685,"external":2366131,"heapTotal":32157696,"heapUsed":19268984,"rss":67264512},"pid":72480,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(561,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(572,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(572,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(572,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(573,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(575,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(575,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(578,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(578,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(580,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(583,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(584,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(592,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(592,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(592,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(593,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(593,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(596,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(596,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(596,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(617,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(618,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:21:32:2132","trace":[{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:21:59 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365237.64},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":101685,"external":2366131,"heapTotal":32227328,"heapUsed":18925576,"rss":67080192},"pid":9204,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:21:59:2159","trace":[{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:22:27 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365265.593},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":101685,"external":2366131,"heapTotal":31965184,"heapUsed":18992544,"rss":67026944},"pid":23276,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:22:27:2227","trace":[{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:22:55 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(314,7): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(314,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(315,7): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(315,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(325,7): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(337,5): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(337,7): error TS1005: 'try' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(340,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,50): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,68): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,82): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(346,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(347,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(347,56): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(348,11): error TS1003: Identifier expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(353,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(353,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(353,36): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(356,21): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(356,59): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(356,61): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(383,5): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(383,7): error TS1005: 'try' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(386,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(391,24): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(391,43): error TS1011: An element access expression should take an argument.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(391,45): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,31): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,70): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(567,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,29): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(574,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(576,22): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,36): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(314,7): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(314,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(315,7): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(315,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(325,7): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(337,5): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(337,7): error TS1005: 'try' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(340,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,50): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,68): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(345,82): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(346,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(347,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(347,56): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(348,11): error TS1003: Identifier expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(353,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(353,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(353,36): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(356,21): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(356,59): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(356,61): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(383,5): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(383,7): error TS1005: 'try' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(386,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(391,24): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(391,43): error TS1011: An element access expression should take an argument.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(391,45): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,31): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,70): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(567,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(572,29): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(574,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(576,22): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(578,36): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365293.203},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":135591,"external":2366131,"heapTotal":32157696,"heapUsed":19227264,"rss":67321856},"pid":3840,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(314,7): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(314,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(315,7): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(315,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(325,7): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(337,5): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(337,7): error TS1005: 'try' expected.\r\nsrc/services/ClientConnectionService.ts(340,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(345,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(345,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(345,50): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(345,68): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(345,82): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(346,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(347,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(347,56): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(348,11): error TS1003: Identifier expected.\r\nsrc/services/ClientConnectionService.ts(353,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(353,17): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(353,36): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(356,21): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(356,59): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(356,61): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(383,7): error TS1005: 'try' expected.\r\nsrc/services/ClientConnectionService.ts(386,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(391,24): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(391,43): error TS1011: An element access expression should take an argument.\r\nsrc/services/ClientConnectionService.ts(391,45): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(444,61): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(492,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,74): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.\r\nsrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(524,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(524,29): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(524,31): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(531,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(531,34): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(545,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(545,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(545,40): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,70): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(567,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(572,29): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(574,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(576,22): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(578,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(578,36): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:22:55:2255","trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
{"date":"Fri Aug 01 2025 16:23:06 GMT+0800 (中国标准时间)","error":{},"exception":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31muncaughtException: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(398,3): error TS1472: 'catch' or 'finally' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(525,12): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(525,77): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,70): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31mError: ⨯ Unable to compile TypeScript:\u001b[39m\n\u001b[31msrc/services/ClientConnectionService.ts(398,3): error TS1472: 'catch' or 'finally' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(524,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(525,12): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(525,77): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,3): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,17): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(550,70): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\u001b[39m\r\n\u001b[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\u001b[39m\r\n\n\u001b[31m    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\u001b[39m\n\u001b[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\u001b[39m\n\u001b[31m    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\u001b[39m\n\u001b[31m    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\u001b[39m\n\u001b[31m    at node:internal/modules/cjs/loader:1895:10\u001b[39m\n\u001b[31m    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\u001b[39m\n\u001b[31m    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\u001b[39m\n\u001b[31m    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\u001b[39m\n\u001b[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)\u001b[39m\n\u001b[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)\u001b[39m","os":{"loadavg":[0,0,0],"uptime":365304.89},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/app.ts"],"cwd":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":127563,"external":2366131,"heapTotal":32227328,"heapUsed":18958496,"rss":67166208},"pid":33668,"uid":null,"version":"v22.16.0"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/ClientConnectionService.ts(398,3): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(444,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(444,61): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,3): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,17): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,74): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.\r\nsrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(524,3): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(524,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(524,29): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(524,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(525,12): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(525,77): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(531,3): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(531,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(531,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(545,3): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(545,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(545,17): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(545,40): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(550,70): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.\r\nsrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.\r\nsrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.\r\nsrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.\r\nsrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.\r\nsrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.\r\nsrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)\n    at Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:69:33)\n    at node:internal/modules/cjs/loader:1895:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)","timestamp":"2025-08-01 16:23:06:236","trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":7,"file":"1)\\xeno-rat-server\\src\\services\\ClientConnectionService.ts","function":"Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":1,"method":"<anonymous> (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":null,"line":1730,"method":null,"native":false},{"column":25,"file":"1)\\xeno-rat-server\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":521,"method":"_compile (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-514192538160436.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"1)\\xeno-rat-server\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","line":63,"method":"ts] (C:\\Users\\<USER>\\Downloads\\xeno-rat-main","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false}]}
