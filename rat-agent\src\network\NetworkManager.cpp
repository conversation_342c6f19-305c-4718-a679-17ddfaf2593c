﻿#include "NetworkManager.h"
#include "Obfuscation.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <chrono>

NetworkManager::NetworkManager()
    : serverPort_(0)
    , clientSocket_(INVALID_SOCKET)
    , webSocketClient_(std::make_unique<WebSocketClient>())
    , isConnected_(false)
    , shouldStop_(false)
{
#ifdef _WIN32
    InitializeWinsock();
#endif
}

NetworkManager::~NetworkManager() {
    Disconnect();
#ifdef _WIN32
    CleanupWinsock();
#endif
}

bool NetworkManager::Initialize(const std::string& host, int port) {
    serverHost_ = host;
    serverPort_ = port;
    
    OBFUSCATE_CODE();
    
    return true;
}

bool NetworkManager::Connect() {
    if (isConnected_) {
        return true;
    }

    // 使用WebSocket客户端连接
    if (!webSocketClient_->Connect(serverHost_, serverPort_)) {
        return false;
    }

    // 设置WebSocket消息回调
    webSocketClient_->SetMessageCallback([this](const std::string& event, const std::string& data) {
        if (messageCallback_) {
            NetworkMessage message;
            message.type = event;
            message.data = data;
            message.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            messageCallback_(message);
        }
    });

    // 设置连接状态回调
    webSocketClient_->SetConnectionCallback([this](bool connected) {
        isConnected_ = connected;
    });

    isConnected_ = webSocketClient_->IsConnected();
    return isConnected_;
}

void NetworkManager::Disconnect() {
    shouldStop_ = true;
    isConnected_ = false;

    if (webSocketClient_) {
        webSocketClient_->Disconnect();
    }

    if (clientSocket_ != INVALID_SOCKET) {
        closesocket(clientSocket_);
        clientSocket_ = INVALID_SOCKET;
    }

    if (receiveThread_.joinable()) {
        receiveThread_.join();
    }
}

bool NetworkManager::SendMessage(const NetworkMessage& message) {
    std::cout << "[DEBUG] SendMessage called - isConnected: " << isConnected_
              << ", webSocketClient valid: " << (webSocketClient_ ? "true" : "false") << std::endl;

    if (!isConnected_ || !webSocketClient_) {
        std::cout << "[DEBUG] SendMessage failed - not connected or no client" << std::endl;
        return false;
    }

    std::lock_guard<std::mutex> lock(sendMutex_);

    try {
        // 使用WebSocket发送消息
        std::cout << "[DEBUG] Calling webSocketClient->Emit..." << std::endl;
        bool result = webSocketClient_->Emit(message.type, message.data);
        std::cout << "[DEBUG] Emit result: " << (result ? "SUCCESS" : "FAILED") << std::endl;
        return result;
    }
    catch (...) {
        std::cout << "[DEBUG] Exception in SendMessage" << std::endl;
        return false;
    }
}

NetworkMessage NetworkManager::ReceiveMessage() {
    // 这个函数在实际使用中不会被直接调用
    // 消息接收通过回调函数处理
    return NetworkMessage();
}

void NetworkManager::SetMessageCallback(std::function<void(const NetworkMessage&)> callback) {
    messageCallback_ = callback;
}

void NetworkManager::SetEncryptionKey(const std::string& key) {
    encryptionKey_ = key;
}

#ifdef _WIN32
bool NetworkManager::InitializeWinsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    return result == 0;
}

void NetworkManager::CleanupWinsock() {
    WSACleanup();
}
#endif

void NetworkManager::ReceiveWorker() {
    while (!shouldStop_ && isConnected_) {
        try {
            // 接收数据长度
            uint32_t networkLength;
            int received = ReceiveRawData(reinterpret_cast<uint8_t*>(&networkLength), sizeof(networkLength));
            
            if (received != sizeof(networkLength)) {
                if (received == 0) {
                    // 连接关闭
                    isConnected_ = false;
                    break;
                }
                continue;
            }
            
            uint32_t dataLength = ntohl(networkLength);
            if (dataLength == 0 || dataLength > 1024 * 1024) {  // 最大1MB
                continue;
            }
            
            // 接收加密数据
            std::vector<uint8_t> encryptedData(dataLength);
            received = ReceiveRawData(encryptedData.data(), dataLength);
            
            if (received != static_cast<int>(dataLength)) {
                continue;
            }
            
            // 解密数据
            auto decryptedData = XORCrypt(encryptedData, encryptionKey_);
            
            // 反序列化消息
            auto message = DeserializeMessage(decryptedData);
            
            // 调用回调函数
            if (messageCallback_) {
                messageCallback_(message);
            }
        }
        catch (...) {
            // 忽略异常，继续接收
        }
        
        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

bool NetworkManager::SendRawData(const std::vector<uint8_t>& data) {
    if (clientSocket_ == INVALID_SOCKET || data.empty()) {
        return false;
    }
    
    size_t totalSent = 0;
    while (totalSent < data.size()) {
        int sent = send(clientSocket_, 
                       reinterpret_cast<const char*>(data.data() + totalSent),
                       static_cast<int>(data.size() - totalSent), 0);
        
        if (sent == SOCKET_ERROR) {
            return false;
        }
        
        totalSent += sent;
    }
    
    return true;
}

int NetworkManager::ReceiveRawData(uint8_t* buffer, int size) {
    if (clientSocket_ == INVALID_SOCKET || !buffer || size <= 0) {
        return -1;
    }
    
    int totalReceived = 0;
    while (totalReceived < size) {
        int received = recv(clientSocket_,
                           reinterpret_cast<char*>(buffer + totalReceived),
                           size - totalReceived, 0);
        
        if (received == SOCKET_ERROR || received == 0) {
            return received == 0 ? totalReceived : -1;
        }
        
        totalReceived += received;
    }
    
    return totalReceived;
}

std::vector<uint8_t> NetworkManager::XORCrypt(const std::vector<uint8_t>& data, const std::string& key) {
    if (data.empty() || key.empty()) {
        return data;
    }

    std::vector<uint8_t> result = data;
    for (size_t i = 0; i < result.size(); ++i) {
        result[i] ^= key[i % key.length()];
    }

    return result;
}

std::vector<uint8_t> NetworkManager::SerializeMessage(const NetworkMessage& message) {
    // 简单的JSON序列化（避免外部依赖）
    std::ostringstream oss;
    oss << "{";
    oss << "\"type\":\"" << message.type << "\",";
    oss << "\"data\":\"" << message.data << "\",";
    oss << "\"timestamp\":" << message.timestamp;
    oss << "}";

    std::string jsonStr = oss.str();
    return std::vector<uint8_t>(jsonStr.begin(), jsonStr.end());
}

NetworkMessage NetworkManager::DeserializeMessage(const std::vector<uint8_t>& data) {
    NetworkMessage message;

    if (data.empty()) {
        return message;
    }

    std::string jsonStr(data.begin(), data.end());

    // 简单的JSON解析（避免外部依赖）
    try {
        size_t typeStart = jsonStr.find("\"type\":\"") + 8;
        size_t typeEnd = jsonStr.find("\"", typeStart);
        if (typeStart != std::string::npos && typeEnd != std::string::npos) {
            message.type = jsonStr.substr(typeStart, typeEnd - typeStart);
        }

        size_t dataStart = jsonStr.find("\"data\":\"") + 8;
        size_t dataEnd = jsonStr.find("\"", dataStart);
        if (dataStart != std::string::npos && dataEnd != std::string::npos) {
            message.data = jsonStr.substr(dataStart, dataEnd - dataStart);
        }

        size_t timestampStart = jsonStr.find("\"timestamp\":") + 12;
        size_t timestampEnd = jsonStr.find_first_of(",}", timestampStart);
        if (timestampStart != std::string::npos && timestampEnd != std::string::npos) {
            std::string timestampStr = jsonStr.substr(timestampStart, timestampEnd - timestampStart);
            message.timestamp = std::stoll(timestampStr);
        }
    }
    catch (...) {
        // 解析失败，返回空消息
        message = NetworkMessage();
    }

    return message;
}

void NetworkManager::Reconnect() {
    if (isConnected_) {
        return;
    }

    // 尝试重连
    for (int i = 0; i < 3; ++i) {
        if (Connect()) {
            break;
        }

        // 等待一段时间后重试
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}
