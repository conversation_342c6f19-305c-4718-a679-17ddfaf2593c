#include <iostream>
#include <thread>
#include <chrono>
#include "src/network/WebSocketClient.h"

int main() {
    std::cout << "=== WebSocket Client Debug Test ===" << std::endl;

    WebSocketClient client;
    
    // 设置回调
    client.SetConnectionCallback([](bool connected) {
        std::cout << "Connection status changed: " << (connected ? "CONNECTED" : "DISCONNECTED") << std::endl;
    });
    
    client.SetMessageCallback([](const std::string& event, const std::string& data) {
        std::cout << "Received message - Event: " << event << ", Data: " << data << std::endl;
    });
    
    std::cout << "Attempting to connect to localhost:3000..." << std::endl;
    
    if (client.Connect("127.0.0.1", 3000)) {
        std::cout << "SUCCESS: Connected to server!" << std::endl;
        
        // 等待一下让连接稳定
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        if (client.IsConnected()) {
            std::cout << "Connection confirmed active" << std::endl;
            
            // 发送测试消息
            std::cout << "Sending test message..." << std::endl;
            if (client.Emit("test", "{\"message\":\"Hello from C++ client\"}")) {
                std::cout << "Test message sent successfully" << std::endl;
            } else {
                std::cout << "Failed to send test message" << std::endl;
            }
            
            // 等待响应
            std::cout << "Waiting for responses..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
        } else {
            std::cout << "Connection not active" << std::endl;
        }
        
        client.Disconnect();
        std::cout << "Disconnected from server" << std::endl;
    } else {
        std::cout << "FAIL: Could not connect to server" << std::endl;
        return 1;
    }
    
    return 0;
}
