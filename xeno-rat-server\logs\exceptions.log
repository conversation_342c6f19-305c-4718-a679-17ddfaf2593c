2025-07-30 21:18:52:1852 [31merror[39m: [31muncaughtException: listen EADDRINUSE: address already in use :::3000[39m
[31mError: listen EADDRINUSE: address already in use :::3000[39m
[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)[39m
[31m    at listenInCluster (node:net:1996:12)[39m
[31m    at Server.listen (node:net:2101:7)[39m
[31m    at startServer (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts:163:12)[39m
2025-07-30 21:43:06:436 [31merror[39m: [31muncaughtException: Cannot read properties of undefined (reading 'values')[39m
[31mTypeError: Cannot read properties of undefined (reading 'values')[39m
[31m    at SocketService.handleConnection (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:93:66)[39m
[31m    at Namespace.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:65:12)[39m
[31m    at Namespace.emit (node:events:518:28)[39m
[31m    at Namespace.emitReserved (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\typed-events.js:56:22)[39m
[31m    at Namespace._doConnect (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\namespace.js:276:14)[39m
[31m    at C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\namespace.js:238:22[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:85:11)[39m
2025-07-30 21:44:09:449 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:44:36:4436 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:44:57:4457 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:45:22:4522 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:47:09:479 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:47:53:4753 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:51:48:5148 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-38644713238247563.js:71:20)[39m
2025-07-30 21:52:00:520 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7953127625139762.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7953127625139762.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7953127625139762.js:71:20)[39m
2025-07-30 21:53:24:5324 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at new ClientConnectionService (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:59:24)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:466:40)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6661483009062084.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6661483009062084.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6661483009062084.js:71:20)[39m
2025-07-30 21:54:13:5413 [31merror[39m: [31muncaughtException: Cannot find module '../middleware/validation'[39m
[31mRequire stack:[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts[39m
[31mError: Cannot find module '../middleware/validation'[39m
[31mRequire stack:[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)[39m
[31m    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)[39m
[31m    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1211:37)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1487:12)[39m
[31m    at require (node:internal/modules/helpers:135:16)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:6:1)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
2025-07-30 21:54:13:5413 [31merror[39m: [31muncaughtException: Cannot find module '../middleware/validation'[39m
[31mRequire stack:[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts[39m
[31mError: Cannot find module '../middleware/validation'[39m
[31mRequire stack:[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)[39m
[31m    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)[39m
[31m    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1211:37)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1487:12)[39m
[31m    at require (node:internal/modules/helpers:135:16)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:6:1)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
2025-07-30 21:58:37:5837 [31merror[39m: [31muncaughtException: Cannot find module 'express-validator'[39m
[31mRequire stack:[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\middleware\validation.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts[39m
[31mError: Cannot find module 'express-validator'[39m
[31mRequire stack:[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\middleware\validation.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts[39m
[31m- C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)[39m
[31m    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)[39m
[31m    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1211:37)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1487:12)[39m
[31m    at require (node:internal/modules/helpers:135:16)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\middleware\validation.ts:2:1)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
2025-07-30 21:59:37:5937 [31merror[39m: [31muncaughtException: Database not initialized. Call initializeDatabase() first.[39m
[31mError: Database not initialized. Call initializeDatabase() first.[39m
[31m    at getDatabase (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\config\database.ts:77:11)[39m
[31m    at new ClientModel (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\models\Client.ts:10:26)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:13:21)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
2025-07-30 22:00:26:026 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:00:47:047 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:01:01:11 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:01:15:115 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-48727188792347786.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:01:38:138 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:02:50:250 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:03:05:35 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:03:18:318 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:03:35:335 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:03:48:348 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:04:00:40 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:04:16:416 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7146969304602313.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 22:04:39:439 [31merror[39m: [31muncaughtException: Cannot set properties of undefined (setting 'message')[39m
[31mTypeError: Cannot set properties of undefined (setting 'message')[39m
[31m    at ValidatorsImpl.withMessage (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\express-validator\lib\chain\validators-impl.js:25:36)[39m
[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:244:29)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-18315345387723614.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-18315345387723614.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-18315345387723614.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
2025-07-30 23:02:23:223 [31merror[39m: [31muncaughtException: req.get is not a function[39m
[31mTypeError: req.get is not a function[39m
[31m    at Object.loginAttempt (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\middleware\requestLogger.ts:182:22)[39m
[31m    at SocketService.handleWebClientConnection (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:170:20)[39m
[31m    at SocketService.handleConnection (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:93:12)[39m
[31m    at Namespace.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:79:12)[39m
[31m    at Namespace.emit (node:events:518:28)[39m
[31m    at Namespace.emitReserved (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\typed-events.js:56:22)[39m
[31m    at Namespace._doConnect (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\namespace.js:276:14)[39m
[31m    at C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\namespace.js:238:22[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:85:11)[39m
2025-07-30 23:03:29:329 [31merror[39m: [31muncaughtException: req.get is not a function[39m
[31mTypeError: req.get is not a function[39m
[31m    at Object.loginAttempt (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\middleware\requestLogger.ts:182:22)[39m
[31m    at SocketService.handleWebClientConnection (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:170:20)[39m
[31m    at SocketService.handleConnection (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:93:12)[39m
[31m    at Namespace.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\SocketService.ts:79:12)[39m
[31m    at Namespace.emit (node:events:518:28)[39m
[31m    at Namespace.emitReserved (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\typed-events.js:56:22)[39m
[31m    at Namespace._doConnect (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\namespace.js:276:14)[39m
[31m    at C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\socket.io\dist\namespace.js:238:22[39m
[31m    at processTicksAndRejections (node:internal/process/task_queues:85:11)[39m
2025-07-30 23:06:29:629 [31merror[39m: [31muncaughtException: listen EADDRINUSE: address already in use :::3000[39m
[31mError: listen EADDRINUSE: address already in use :::3000[39m
[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)[39m
[31m    at listenInCluster (node:net:1996:12)[39m
[31m    at Server.listen (node:net:2101:7)[39m
[31m    at startServer (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts:169:12)[39m
2025-07-31 10:21:03:213 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/routes/clients.ts(658,13): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/routes/clients.ts(658,13): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\routes\clients.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4911100814153906.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4911100814153906.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-4911100814153906.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-07-31 10:37:27:3727 [31merror[39m: [31muncaughtException: listen EADDRINUSE: address already in use :::3000[39m
[31mError: listen EADDRINUSE: address already in use :::3000[39m
[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)[39m
[31m    at listenInCluster (node:net:1996:12)[39m
[31m    at Server.listen (node:net:2101:7)[39m
[31m    at startServer (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts:173:12)[39m
2025-08-01 01:02:32:232 [31merror[39m: [31muncaughtException: listen EADDRINUSE: address already in use :::3000[39m
[31mError: listen EADDRINUSE: address already in use :::3000[39m
[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)[39m
[31m    at listenInCluster (node:net:1996:12)[39m
[31m    at Server.listen (node:net:2101:7)[39m
[31m    at startServer (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\dist\app.js:124:16)[39m
2025-08-01 01:31:16:3116 [31merror[39m: [31muncaughtException: listen EADDRINUSE: address already in use :::3000[39m
[31mError: listen EADDRINUSE: address already in use :::3000[39m
[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)[39m
[31m    at listenInCluster (node:net:1996:12)[39m
[31m    at Server.listen (node:net:2101:7)[39m
[31m    at startServer (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts:173:12)[39m
2025-08-01 14:33:36:3336 [31merror[39m: [31muncaughtException: listen EADDRINUSE: address already in use :::3000[39m
[31mError: listen EADDRINUSE: address already in use :::3000[39m
[31m    at Server.setupListenHandle [as _listen2] (node:net:1939:16)[39m
[31m    at listenInCluster (node:net:1996:12)[39m
[31m    at Server.listen (node:net:2101:7)[39m
[31m    at startServer (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\app.ts:173:12)[39m
2025-08-01 16:20:06:206 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(496,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(507,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(507,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(507,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(507,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(510,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(510,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(515,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(518,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(519,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(527,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(527,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(527,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(528,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(528,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(552,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(553,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(496,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(507,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(507,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(507,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(507,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(510,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(510,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(515,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(518,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(519,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(527,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(527,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(527,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(528,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(528,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(552,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(553,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:20:35:2035 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(517,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(528,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(528,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(528,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(528,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(529,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(536,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(539,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(540,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(548,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(549,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(549,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(573,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(574,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(517,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(528,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(528,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(528,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(528,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(529,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(536,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(539,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(540,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(548,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(549,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(549,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(573,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(574,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:21:04:214 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(538,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(549,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(549,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(549,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(549,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(550,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(555,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(555,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(557,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(560,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(561,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(569,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(569,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(569,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(570,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(570,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(573,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(573,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(573,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(594,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(595,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(538,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(549,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(549,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(549,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(549,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(550,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(552,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(555,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(555,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(557,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(560,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(561,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(569,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(569,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(569,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(570,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(570,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(573,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(573,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(573,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(594,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(595,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:21:32:2132 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(561,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(572,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(572,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(572,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(573,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(575,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(575,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(580,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(583,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(584,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(592,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(592,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(592,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(617,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(618,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(561,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(572,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(572,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(572,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(573,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(575,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(575,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(580,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(583,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(584,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(592,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(592,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(592,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(617,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(618,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:21:59:2159 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:22:27:2227 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:22:55:2255 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(314,7): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(314,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(315,7): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(315,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(325,7): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(337,5): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(337,7): error TS1005: 'try' expected.[39m
[31msrc/services/ClientConnectionService.ts(340,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(345,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(345,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(345,50): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(345,68): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(345,82): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(346,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(347,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(347,56): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(348,11): error TS1003: Identifier expected.[39m
[31msrc/services/ClientConnectionService.ts(353,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(353,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(353,36): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(356,21): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(356,59): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(356,61): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(383,5): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(383,7): error TS1005: 'try' expected.[39m
[31msrc/services/ClientConnectionService.ts(386,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(391,24): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(391,43): error TS1011: An element access expression should take an argument.[39m
[31msrc/services/ClientConnectionService.ts(391,45): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(492,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.[39m
[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,31): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(531,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(545,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.[39m
[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,70): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(567,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(572,29): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(574,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(576,22): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,36): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(314,7): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(314,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(315,7): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(315,17): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(325,7): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(337,5): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(337,7): error TS1005: 'try' expected.[39m
[31msrc/services/ClientConnectionService.ts(340,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(345,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(345,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(345,50): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(345,68): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(345,82): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(346,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(347,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(347,56): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(348,11): error TS1003: Identifier expected.[39m
[31msrc/services/ClientConnectionService.ts(353,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(353,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(353,36): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(356,21): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(356,59): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(356,61): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(383,5): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(383,7): error TS1005: 'try' expected.[39m
[31msrc/services/ClientConnectionService.ts(386,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(391,24): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(391,43): error TS1011: An element access expression should take an argument.[39m
[31msrc/services/ClientConnectionService.ts(391,45): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(492,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.[39m
[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,31): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(531,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(545,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.[39m
[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,70): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(567,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(572,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(572,29): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(574,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(576,22): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(578,36): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
2025-08-01 16:23:06:236 [31merror[39m: [31muncaughtException: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(398,3): error TS1472: 'catch' or 'finally' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(444,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.[39m
[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(525,12): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(525,77): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.[39m
[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,70): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31mError: ⨯ Unable to compile TypeScript:[39m
[31msrc/services/ClientConnectionService.ts(398,3): error TS1472: 'catch' or 'finally' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,19): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(398,21): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(405,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,40): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(405,71): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,53): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,59): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(412,61): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(427,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,41): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,47): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(427,49): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(444,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(444,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(444,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(444,61): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(445,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(446,32): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(449,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(465,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(468,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(470,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(473,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(475,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(478,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(486,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,48): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,74): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(492,88): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(493,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(494,56): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(495,11): error TS1003: Identifier expected.[39m
[31msrc/services/ClientConnectionService.ts(500,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(500,74): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(503,17): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(508,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(511,69): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(513,47): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(514,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,29): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(524,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(525,12): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(525,77): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(531,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(532,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(534,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(536,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(539,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,3): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,17): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,40): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(545,54): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(546,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(546,27): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(547,64): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,11): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,30): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,39): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(548,41): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(548,45): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,10): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.[39m
[31msrc/services/ClientConnectionService.ts(550,16): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,39): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,42): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,46): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,68): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(550,70): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(582,3): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.[39m
[31msrc/services/ClientConnectionService.ts(593,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(593,11): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(593,33): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(593,47): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(594,9): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,13): error TS1005: ':' expected.[39m
[31msrc/services/ClientConnectionService.ts(596,78): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,24): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(599,38): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(601,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(604,9): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(605,7): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,3): error TS1434: Unexpected keyword or identifier.[39m
[31msrc/services/ClientConnectionService.ts(613,16): error TS1005: ';' expected.[39m
[31msrc/services/ClientConnectionService.ts(613,30): error TS1109: Expression expected.[39m
[31msrc/services/ClientConnectionService.ts(614,11): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(614,34): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,13): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,31): error TS1005: ',' expected.[39m
[31msrc/services/ClientConnectionService.ts(617,33): error TS1136: Property assignment expected.[39m
[31msrc/services/ClientConnectionService.ts(638,3): error TS1128: Declaration or statement expected.[39m
[31msrc/services/ClientConnectionService.ts(639,1): error TS1128: Declaration or statement expected.[39m

[31m    at Object.<anonymous> (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\src\services\ClientConnectionService.ts:1:7)[39m
[31m    at Module.<anonymous> (node:internal/modules/cjs/loader:1730:14)[39m
[31m    at Module._compile (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\source-map-support\source-map-support.js:521:25)[39m
[31m    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:69:33)[39m
[31m    at node:internal/modules/cjs/loader:1895:10[39m
[31m    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:114:20)[39m
[31m    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-514192538160436.js:71:20)[39m
[31m    at Object.nodeDevHook [as .ts] (C:\Users\<USER>\Downloads\xeno-rat-main (1)\xeno-rat-server\node_modules\ts-node-dev\lib\hook.js:63:13)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1465:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1282:12)[39m
