﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\test_command_execute.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\test_json.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\agent.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\test_real_connection.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\test_simple_dispatcher.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\test_socketio_debug.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\TestAgent.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Debug\test_websocket_heartbeat.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>