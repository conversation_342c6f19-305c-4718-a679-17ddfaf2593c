#include <iostream>
#include <thread>
#include <chrono>
#include "src/network/WebSocketClient.h"

int main() {
    std::cout << "=== WebSocket Heartbeat Test ===" << std::endl;
    
    WebSocketClient client;
    
    // 设置连接状态回调
    client.SetConnectionCallback([](bool connected) {
        std::cout << "[CALLBACK] Connection status: " << (connected ? "CONNECTED" : "DISCONNECTED") << std::endl;
    });
    
    // 设置消息回调
    client.SetMessageCallback([](const std::string& event, const std::string& data) {
        std::cout << "[CALLBACK] Received - Event: " << event << ", Data: " << data << std::endl;
    });
    
    std::cout << "Connecting to localhost:3000..." << std::endl;
    if (!client.Connect("127.0.0.1", 3000)) {
        std::cout << "Failed to connect!" << std::endl;
        return 1;
    }
    
    std::cout << "Connected! Monitoring for 120 seconds..." << std::endl;
    
    // 监控连接120秒
    for (int i = 0; i < 120; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        if (!client.IsConnected()) {
            std::cout << "Connection lost after " << i << " seconds!" << std::endl;
            break;
        }
        
        // 每10秒输出状态
        if (i % 10 == 0) {
            std::cout << "Still connected after " << i << " seconds..." << std::endl;
        }
    }
    
    if (client.IsConnected()) {
        std::cout << "Connection maintained for full 120 seconds!" << std::endl;
    }
    
    client.Disconnect();
    std::cout << "Test completed." << std::endl;
    
    return 0;
}
