﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{192ADF90-376C-33E4-BD19-B133C35AA938}"
	ProjectSection(ProjectDependencies) = postProject
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0} = {F803627F-65B4-3DA5-8E39-719BFC27C3D0}
		{9C90FAD6-EC6F-3784-8355-426602080279} = {9C90FAD6-EC6F-3784-8355-426602080279}
		{680189B3-2E33-3D21-BB70-933ABEDDDA29} = {680189B3-2E33-3D21-BB70-933ABEDDDA29}
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF} = {38DADE39-B72C-3EBA-88E6-A9CDBB7A6EC<PERSON>}
		{CF4838F0-488F-3707-AB8F-353F8200733F} = {CF4838F0-488F-3707-AB8F-353F8200733F}
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C} = {A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}
		{867EF2F6-8153-3573-8B07-C21DB9464029} = {867EF2F6-8153-3573-8B07-C21DB9464029}
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F} = {9B15D1EE-B2E5-38CA-930B-59532D64C16F}
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF} = {BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CommandExecuteTest", "CommandExecuteTest.vcxproj", "{F803627F-65B4-3DA5-8E39-719BFC27C3D0}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{BDAAF11C-7D36-3240-A21F-0E493B532318}"
	ProjectSection(ProjectDependencies) = postProject
		{192ADF90-376C-33E4-BD19-B133C35AA938} = {192ADF90-376C-33E4-BD19-B133C35AA938}
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "JsonTest", "JsonTest.vcxproj", "{9C90FAD6-EC6F-3784-8355-426602080279}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MinimalAgent", "MinimalAgent.vcxproj", "{680189B3-2E33-3D21-BB70-933ABEDDDA29}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RealConnectionTest", "RealConnectionTest.vcxproj", "{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SimpleConnectionTest", "SimpleConnectionTest.vcxproj", "{CF4838F0-488F-3707-AB8F-353F8200733F}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SimpleDispatcherTest", "SimpleDispatcherTest.vcxproj", "{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SocketIODebugTest", "SocketIODebugTest.vcxproj", "{867EF2F6-8153-3573-8B07-C21DB9464029}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "TestAgent", "TestAgent.vcxproj", "{9B15D1EE-B2E5-38CA-930B-59532D64C16F}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "WebSocketHeartbeatTest", "WebSocketHeartbeatTest.vcxproj", "{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{C8D39115-0441-3B94-B77D-A2902D9D4D3A}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "clean-all", "clean-all.vcxproj", "{9CE5D326-FBBC-3E99-A9F1-CC7CB5EF0D36}"
	ProjectSection(ProjectDependencies) = postProject
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "package", "package.vcxproj", "{03A3682D-F4FB-3D3B-8175-F12668137553}"
	ProjectSection(ProjectDependencies) = postProject
		{680189B3-2E33-3D21-BB70-933ABEDDDA29} = {680189B3-2E33-3D21-BB70-933ABEDDDA29}
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A} = {C8D39115-0441-3B94-B77D-A2902D9D4D3A}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{192ADF90-376C-33E4-BD19-B133C35AA938}.Debug|x64.ActiveCfg = Debug|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.Debug|x64.Build.0 = Debug|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.Release|x64.ActiveCfg = Release|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.Release|x64.Build.0 = Release|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{192ADF90-376C-33E4-BD19-B133C35AA938}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.Debug|x64.ActiveCfg = Debug|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.Debug|x64.Build.0 = Debug|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.Release|x64.ActiveCfg = Release|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.Release|x64.Build.0 = Release|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F803627F-65B4-3DA5-8E39-719BFC27C3D0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BDAAF11C-7D36-3240-A21F-0E493B532318}.Debug|x64.ActiveCfg = Debug|x64
		{BDAAF11C-7D36-3240-A21F-0E493B532318}.Release|x64.ActiveCfg = Release|x64
		{BDAAF11C-7D36-3240-A21F-0E493B532318}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BDAAF11C-7D36-3240-A21F-0E493B532318}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.Debug|x64.ActiveCfg = Debug|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.Debug|x64.Build.0 = Debug|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.Release|x64.ActiveCfg = Release|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.Release|x64.Build.0 = Release|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9C90FAD6-EC6F-3784-8355-426602080279}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.Debug|x64.ActiveCfg = Debug|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.Debug|x64.Build.0 = Debug|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.Release|x64.ActiveCfg = Release|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.Release|x64.Build.0 = Release|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{680189B3-2E33-3D21-BB70-933ABEDDDA29}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.Debug|x64.ActiveCfg = Debug|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.Debug|x64.Build.0 = Debug|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.Release|x64.ActiveCfg = Release|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.Release|x64.Build.0 = Release|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{38DADE39-B72C-3EBA-88E6-A9CDBB7A6ECF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.Debug|x64.ActiveCfg = Debug|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.Debug|x64.Build.0 = Debug|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.Release|x64.ActiveCfg = Release|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.Release|x64.Build.0 = Release|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CF4838F0-488F-3707-AB8F-353F8200733F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.Debug|x64.ActiveCfg = Debug|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.Debug|x64.Build.0 = Debug|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.Release|x64.ActiveCfg = Release|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.Release|x64.Build.0 = Release|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A3B89D7B-AC95-3213-82D3-77FD0A8C3A8C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.Debug|x64.ActiveCfg = Debug|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.Debug|x64.Build.0 = Debug|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.Release|x64.ActiveCfg = Release|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.Release|x64.Build.0 = Release|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{867EF2F6-8153-3573-8B07-C21DB9464029}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.Debug|x64.ActiveCfg = Debug|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.Debug|x64.Build.0 = Debug|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.Release|x64.ActiveCfg = Release|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.Release|x64.Build.0 = Release|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9B15D1EE-B2E5-38CA-930B-59532D64C16F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.Debug|x64.ActiveCfg = Debug|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.Debug|x64.Build.0 = Debug|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.Release|x64.ActiveCfg = Release|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.Release|x64.Build.0 = Release|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BB6C2E90-92DE-31FE-903A-2123C3BDC5EF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.Debug|x64.ActiveCfg = Debug|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.Debug|x64.Build.0 = Debug|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.Release|x64.ActiveCfg = Release|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.Release|x64.Build.0 = Release|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C8D39115-0441-3B94-B77D-A2902D9D4D3A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9CE5D326-FBBC-3E99-A9F1-CC7CB5EF0D36}.Debug|x64.ActiveCfg = Debug|x64
		{9CE5D326-FBBC-3E99-A9F1-CC7CB5EF0D36}.Release|x64.ActiveCfg = Release|x64
		{9CE5D326-FBBC-3E99-A9F1-CC7CB5EF0D36}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9CE5D326-FBBC-3E99-A9F1-CC7CB5EF0D36}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{03A3682D-F4FB-3D3B-8175-F12668137553}.Debug|x64.ActiveCfg = Debug|x64
		{03A3682D-F4FB-3D3B-8175-F12668137553}.Release|x64.ActiveCfg = Release|x64
		{03A3682D-F4FB-3D3B-8175-F12668137553}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{03A3682D-F4FB-3D3B-8175-F12668137553}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {416F918A-35D2-3341-AA58-C29C0AFDBE23}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
