#pragma once

#include <string>
#include <vector>
#include <functional>
#include <atomic>
#include <thread>
#include <mutex>
#include <queue>
#include <memory>
#include "WebSocketClient.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#define SOCKET int
#define INVALID_SOCKET -1
#define SOCKET_ERROR -1
#define closesocket close
#endif

/**
 * 网络消息结构
 */
struct NetworkMessage {
    std::string type;
    std::string data;
    long long timestamp;
    
    NetworkMessage(const std::string& t = "", const std::string& d = "", long long ts = 0)
        : type(t), data(d), timestamp(ts) {}
};

/**
 * 网络管理器
 * 负责与服务器的网络通信、加密解密、消息收发等
 */
class NetworkManager {
public:
    NetworkManager();
    ~NetworkManager();
    
    /**
     * 初始化网络管理器
     * @param host 服务器地址
     * @param port 服务器端口
     * @return 初始化是否成功
     */
    bool Initialize(const std::string& host, int port);
    
    /**
     * 连接到服务器
     * @return 连接是否成功
     */
    bool Connect();
    
    /**
     * 断开连接
     */
    void Disconnect();
    
    /**
     * 发送消息
     * @param message 要发送的消息
     * @return 发送是否成功
     */
    bool SendMessage(const NetworkMessage& message);
    
    /**
     * 接收消息
     * @return 接收到的消息，如果没有消息则返回空消息
     */
    NetworkMessage ReceiveMessage();
    
    /**
     * 检查连接状态
     * @return 是否已连接
     */
    bool IsConnected() const { return isConnected_; }
    
    /**
     * 设置消息接收回调
     * @param callback 消息接收回调函数
     */
    void SetMessageCallback(std::function<void(const NetworkMessage&)> callback);
    
    /**
     * 设置加密密钥
     * @param key 加密密钥
     */
    void SetEncryptionKey(const std::string& key);

private:
    // 网络配置
    std::string serverHost_;
    int serverPort_;
    SOCKET clientSocket_;

    // WebSocket客户端
    std::unique_ptr<WebSocketClient> webSocketClient_;

    // 连接状态
    std::atomic<bool> isConnected_;
    std::atomic<bool> shouldStop_;
    
    // 加密相关
    std::string encryptionKey_;
    
    // 消息处理
    std::function<void(const NetworkMessage&)> messageCallback_;
    std::thread receiveThread_;
    std::mutex sendMutex_;
    std::queue<NetworkMessage> sendQueue_;
    
    /**
     * 初始化Winsock (Windows only)
     */
    bool InitializeWinsock();
    
    /**
     * 清理Winsock (Windows only)
     */
    void CleanupWinsock();
    
    /**
     * 接收线程函数
     */
    void ReceiveWorker();
    
    /**
     * 发送原始数据
     * @param data 要发送的数据
     * @return 发送是否成功
     */
    bool SendRawData(const std::vector<uint8_t>& data);
    
    /**
     * 接收原始数据
     * @param buffer 接收缓冲区
     * @param size 缓冲区大小
     * @return 实际接收的字节数，-1表示错误
     */
    int ReceiveRawData(uint8_t* buffer, int size);
    
    /**
     * 异或加密/解密
     * @param data 要加密/解密的数据
     * @param key 密钥
     * @return 加密/解密后的数据
     */
    std::vector<uint8_t> XORCrypt(const std::vector<uint8_t>& data, const std::string& key);
    
    /**
     * 序列化消息
     * @param message 要序列化的消息
     * @return 序列化后的数据
     */
    std::vector<uint8_t> SerializeMessage(const NetworkMessage& message);
    
    /**
     * 反序列化消息
     * @param data 序列化的数据
     * @return 反序列化后的消息
     */
    NetworkMessage DeserializeMessage(const std::vector<uint8_t>& data);
    
    /**
     * 重连到服务器
     */
    void Reconnect();
    
    // 禁用拷贝构造和赋值
    NetworkManager(const NetworkManager&) = delete;
    NetworkManager& operator=(const NetworkManager&) = delete;
};
