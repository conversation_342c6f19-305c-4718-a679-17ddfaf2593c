{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/health","query":{},"requestId":"60655bbf-ce84-4d10-9b7c-f26cbca8fe5f","timestamp":"2025-08-01 00:02:41:241","url":"/health","userAgent":"RAT-Agent/1.0"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 00:02:41:241"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"343000μs","timestamp":"2025-08-01 00:02:41:241","user":"375000μs"}
{"contentLength":"377","duration":"5ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"60655bbf-ce84-4d10-9b7c-f26cbca8fe5f","statusCode":200,"timestamp":"2025-08-01 00:02:41:241","url":"/health","userAgent":"RAT-Agent/1.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:16:02:41 +0000] \"GET /health HTTP/1.1\" 200 377 \"-\" \"RAT-Agent/1.0\"\u001b[39m","timestamp":"2025-08-01 00:02:41:241"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/","query":{},"requestId":"735f8fc7-57bb-418e-816f-ccfef1e724a3","timestamp":"2025-08-01 00:02:41:241","url":"/","userAgent":"RAT-Agent/1.0"}
{"contentLength":"161","duration":"1ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"735f8fc7-57bb-418e-816f-ccfef1e724a3","statusCode":200,"timestamp":"2025-08-01 00:02:41:241","url":"/","userAgent":"RAT-Agent/1.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:16:02:41 +0000] \"GET / HTTP/1.1\" 200 161 \"-\" \"RAT-Agent/1.0\"\u001b[39m","timestamp":"2025-08-01 00:02:41:241"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 00:04:13:413"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"343000μs","timestamp":"2025-08-01 00:04:13:413","user":"375000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 00:04:13:413"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 00:08:35:835","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 00:08:35:835"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 00:08:35:835"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"60MB","timestamp":"2025-08-01 00:13:35:1335"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"60MB","timestamp":"2025-08-01 00:13:35:1335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"187000μs","timestamp":"2025-08-01 00:13:35:1335","user":"312000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"60MB","timestamp":"2025-08-01 00:18:35:1835"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"60MB","timestamp":"2025-08-01 00:18:35:1835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"187000μs","timestamp":"2025-08-01 00:18:35:1835","user":"312000μs"}
{"headers":{},"ip":"::ffff:127.0.0.1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/","query":{},"requestId":"718859c2-99d9-4b80-a352-f3aea1c16691","timestamp":"2025-08-01 00:19:53:1953","url":"/"}
{"contentLength":"161","duration":"8ms","ip":"::ffff:127.0.0.1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"718859c2-99d9-4b80-a352-f3aea1c16691","statusCode":200,"timestamp":"2025-08-01 00:19:53:1953","url":"/"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::ffff:127.0.0.1 - - [31/Jul/2025:16:19:53 +0000] \"GET / HTTP/1.1\" 200 161 \"-\" \"-\"\u001b[39m","timestamp":"2025-08-01 00:19:53:1953"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 00:23:35:2335"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 00:23:35:2335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"187000μs","timestamp":"2025-08-01 00:23:35:2335","user":"328000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 00:28:35:2835"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 00:28:35:2835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"187000μs","timestamp":"2025-08-01 00:28:35:2835","user":"328000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 00:33:35:3335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"187000μs","timestamp":"2025-08-01 00:33:35:3335","user":"343000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 00:33:35:3335"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 00:38:35:3835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"203000μs","timestamp":"2025-08-01 00:38:35:3835","user":"343000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 00:38:35:3835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket nUZ3pG14s4QX5ydhAAAK\u001b[39m","timestamp":"2025-08-01 00:40:10:4010"}
{"clientId":"test_rat_client_001","hostname":"Unknown","ipAddress":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 00:40:10:4010"}
{"action":"新客户端连接","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:40:10:4010","userId":"test_rat_client_001"}
{"clientId":"test_rat_client_001","hostname":"Unknown","ipAddress":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:40:10:4010","username":"Unknown"}
{"clientId":"test_rat_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket nUZ3pG14s4QX5ydhAAAK, 原因: client namespace disconnect\u001b[39m","timestamp":"2025-08-01 00:40:15:4015"}
{"action":"Socket断开: client namespace disconnect","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:40:15:4015","userId":"test_rat_client_001"}
{"clientId":"test_rat_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: client namespace disconnect","timestamp":"2025-08-01 00:40:15:4015","连接时长":"5秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket dIaWy7ewGQOxhmI1AAAM\u001b[39m","timestamp":"2025-08-01 00:42:19:4219"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 00:42:19:4219"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:42:19:4219","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:42:19:4219","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:42:19:4219"}
{"clientId":"client_1753980142784_qqvo79mqw","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 00:42:22:4222"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:42:22:4222","userId":"client_1753980142784_qqvo79mqw"}
{"clientId":"client_1753980142784_qqvo79mqw","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Windows 10","timestamp":"2025-08-01 00:42:22:4222","username":"test-user"}
{"clientId":"client_1753980142784_qqvo79mqw","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:42:22:4222"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"58MB","timestamp":"2025-08-01 00:43:35:4335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"203000μs","timestamp":"2025-08-01 00:43:35:4335","user":"421000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 2 clients as offline\u001b[39m","timestamp":"2025-08-01 00:43:35:4335"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"58MB","timestamp":"2025-08-01 00:43:35:4335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket dIaWy7ewGQOxhmI1AAAM, 原因: ping timeout\u001b[39m","timestamp":"2025-08-01 00:43:44:4344"}
{"action":"Socket断开: ping timeout","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:43:44:4344","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: ping timeout","timestamp":"2025-08-01 00:43:44:4344","连接时长":"85秒"}
{"action":"Socket断开: ping timeout","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:43:44:4344","userId":"client_1753980142784_qqvo79mqw"}
{"clientId":"client_1753980142784_qqvo79mqw","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: ping timeout","timestamp":"2025-08-01 00:43:44:4344","连接时长":"82秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket dHi7t9kSY78FKlhnAAAO\u001b[39m","timestamp":"2025-08-01 00:47:06:476"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:47:06:476","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:47:06:476","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:47:06:476"}
{"clientId":"client_1753980429417_oa1wjx6jy","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 00:47:09:479"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:47:09:479","userId":"client_1753980429417_oa1wjx6jy"}
{"clientId":"client_1753980429417_oa1wjx6jy","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Windows 10","timestamp":"2025-08-01 00:47:09:479","username":"test-user"}
{"clientId":"client_1753980429417_oa1wjx6jy","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket dHi7t9kSY78FKlhnAAAO, 原因: ping timeout\u001b[39m","timestamp":"2025-08-01 00:48:31:4831"}
{"action":"Socket断开: ping timeout","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:48:31:4831","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: ping timeout","timestamp":"2025-08-01 00:48:31:4831","连接时长":"85秒"}
{"action":"Socket断开: ping timeout","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:48:31:4831","userId":"client_1753980429417_oa1wjx6jy"}
{"clientId":"client_1753980429417_oa1wjx6jy","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: ping timeout","timestamp":"2025-08-01 00:48:31:4831","连接时长":"82秒"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"55MB","timestamp":"2025-08-01 00:48:35:4835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 00:48:35:4835","user":"515000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"55MB","timestamp":"2025-08-01 00:48:35:4835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket AmFv6ybxbjWVP2WQAAAQ\u001b[39m","timestamp":"2025-08-01 00:49:27:4927"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:49:27:4927","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:49:27:4927","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:49:27:4927"}
{"clientId":"client_1753980570399_mhm8ya0md","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 00:49:30:4930"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:49:30:4930","userId":"client_1753980570399_mhm8ya0md"}
{"clientId":"client_1753980570399_mhm8ya0md","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Windows 10","timestamp":"2025-08-01 00:49:30:4930","username":"test-user"}
{"clientId":"client_1753980570399_mhm8ya0md","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:49:30:4930"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket MqNbirUsnIHwTz5aAAAS\u001b[39m","timestamp":"2025-08-01 00:49:37:4937"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket AmFv6ybxbjWVP2WQAAAQ, 原因: server namespace disconnect\u001b[39m","timestamp":"2025-08-01 00:49:37:4937"}
{"action":"Socket断开: server namespace disconnect","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:49:37:4937","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: server namespace disconnect","timestamp":"2025-08-01 00:49:37:4937","连接时长":"11秒"}
{"action":"Socket断开: server namespace disconnect","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:49:37:4937","userId":"client_1753980570399_mhm8ya0md"}
{"clientId":"client_1753980570399_mhm8ya0md","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: server namespace disconnect","timestamp":"2025-08-01 00:49:37:4937","连接时长":"8秒"}
{"action":"重复连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:49:37:4937","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"重复连接","timestamp":"2025-08-01 00:49:37:4937","连接时长":"11秒"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:49:37:4937","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:49:37:4937","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:49:37:4937"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket MqNbirUsnIHwTz5aAAAS, 原因: transport error\u001b[39m","timestamp":"2025-08-01 00:49:49:4949"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:49:49:4949","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 00:49:49:4949","连接时长":"12秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket gnUgyX2L0-O2n9_CAAAU\u001b[39m","timestamp":"2025-08-01 00:50:25:5025"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:50:25:5025","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:50:25:5025","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:50:25:5025"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket gnUgyX2L0-O2n9_CAAAU, 原因: transport error\u001b[39m","timestamp":"2025-08-01 00:50:31:5031"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:50:31:5031","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 00:50:31:5031","连接时长":"6秒"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"52MB","timestamp":"2025-08-01 00:53:35:5335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 00:53:35:5335","user":"593000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"52MB","timestamp":"2025-08-01 00:53:35:5335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket KAX-ors0ggpILtPaAAAW\u001b[39m","timestamp":"2025-08-01 00:54:02:542"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 00:54:02:542","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 00:54:02:542","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 00:54:02:542"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket KAX-ors0ggpILtPaAAAW, 原因: transport error\u001b[39m","timestamp":"2025-08-01 00:54:21:5421"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 00:54:21:5421","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 00:54:21:5421","连接时长":"19秒"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"50MB","timestamp":"2025-08-01 00:58:35:5835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 00:58:35:5835","user":"593000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"50MB","timestamp":"2025-08-01 00:58:35:5835"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 01:02:32:232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket q3pRU-uXE6hqB23QAAAY\u001b[39m","timestamp":"2025-08-01 01:03:19:319"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 01:03:19:319"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 01:03:19:319","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 01:03:19:319","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 01:03:19:319"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"49MB","timestamp":"2025-08-01 01:03:35:335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 01:03:35:335","user":"593000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 01:03:35:335"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"49MB","timestamp":"2025-08-01 01:03:35:335"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/","query":{},"requestId":"21539917-ca7e-4e0d-8ae8-3e648585f427","timestamp":"2025-08-01 01:04:03:43","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"161","duration":"3ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"21539917-ca7e-4e0d-8ae8-3e648585f427","statusCode":200,"timestamp":"2025-08-01 01:04:03:43","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:04:03 +0000] \"GET / HTTP/1.1\" 200 161 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 01:04:03:43"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/favicon.ico","query":{},"requestId":"84465398-bf1d-441c-8c56-93d69e4db0c4","timestamp":"2025-08-01 01:04:04:44","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m路由未找到:\u001b[39m","method":"GET","timestamp":"2025-08-01 01:04:04:44","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"135","duration":"2ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"84465398-bf1d-441c-8c56-93d69e4db0c4","statusCode":404,"timestamp":"2025-08-01 01:04:04:44","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:04:04 +0000] \"GET /favicon.ico HTTP/1.1\" 404 135 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 01:04:04:44"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket q3pRU-uXE6hqB23QAAAY, 原因: ping timeout\u001b[39m","timestamp":"2025-08-01 01:04:44:444"}
{"action":"Socket断开: ping timeout","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 01:04:44:444","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: ping timeout","timestamp":"2025-08-01 01:04:44:444","连接时长":"85秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket CoR7MQol1N50lYDAAAAc\u001b[39m","timestamp":"2025-08-01 01:07:21:721"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"51MB","timestamp":"2025-08-01 01:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 01:08:35:835","user":"750000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"51MB","timestamp":"2025-08-01 01:08:35:835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket cGngFgTbhzGqVejmAAAe\u001b[39m","timestamp":"2025-08-01 01:13:15:1315"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 01:13:15:1315","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 01:13:15:1315","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 01:13:15:1315"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"51MB","timestamp":"2025-08-01 01:13:35:1335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 01:13:35:1335","user":"750000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 01:13:35:1335"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"51MB","timestamp":"2025-08-01 01:13:35:1335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket cGngFgTbhzGqVejmAAAe, 原因: transport error\u001b[39m","timestamp":"2025-08-01 01:13:35:1335"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 01:13:35:1335","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 01:13:35:1335","连接时长":"20秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket JGYId4wv-FUGx3AzAAAg\u001b[39m","timestamp":"2025-08-01 01:17:15:1715"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 01:17:15:1715","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 01:17:15:1715","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 01:17:15:1715"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket JGYId4wv-FUGx3AzAAAg, 原因: transport error\u001b[39m","timestamp":"2025-08-01 01:17:35:1735"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 01:17:35:1735","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 01:17:35:1735","连接时长":"20秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket QEWloz3ZE6umTqFWAAAi\u001b[39m","timestamp":"2025-08-01 01:17:57:1757"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"50MB","timestamp":"2025-08-01 01:18:35:1835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 01:18:35:1835","user":"765000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"50MB","timestamp":"2025-08-01 01:18:35:1835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket CoR7MQol1N50lYDAAAAc, 原因: transport close\u001b[39m","timestamp":"2025-08-01 01:19:53:1953"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"49MB","timestamp":"2025-08-01 01:23:35:2335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 01:23:35:2335","user":"781000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"49MB","timestamp":"2025-08-01 01:23:35:2335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket QEWloz3ZE6umTqFWAAAi, 原因: client namespace disconnect\u001b[39m","timestamp":"2025-08-01 01:24:48:2448"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/api/clients","query":{},"requestId":"2eda6ea5-2589-4b64-bd49-3575152f0b1b","timestamp":"2025-08-01 01:26:18:2618","url":"/api/clients","userAgent":"axios/1.11.0"}
{"contentLength":"75","duration":"6ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"2eda6ea5-2589-4b64-bd49-3575152f0b1b","statusCode":401,"timestamp":"2025-08-01 01:26:18:2618","url":"/","userAgent":"axios/1.11.0"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 认证/授权失败\u001b[39m","method":"GET","requestId":"2eda6ea5-2589-4b64-bd49-3575152f0b1b","statusCode":401,"timestamp":"2025-08-01 01:26:18:2618","url":"/","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:26:19 +0000] \"GET /api/clients HTTP/1.1\" 401 75 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:26:19:2619"}
{"duration":"6ms","endpoint":"GET /","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":401,"timestamp":"2025-08-01 01:26:19:2619","userAgent":"axios/1.11.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket EakcNQm0XRGf40DFAAAk\u001b[39m","timestamp":"2025-08-01 01:28:09:289"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 01:28:09:289","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 01:28:09:289","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 01:28:09:289"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket EakcNQm0XRGf40DFAAAk, 原因: transport error\u001b[39m","timestamp":"2025-08-01 01:28:29:2829"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 01:28:29:2829","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 01:28:29:2829","连接时长":"20秒"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"49MB","timestamp":"2025-08-01 01:28:35:2835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"265000μs","timestamp":"2025-08-01 01:28:35:2835","user":"828000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"49MB","timestamp":"2025-08-01 01:28:35:2835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket RSIxL1h75U1aZQSbAAAm\u001b[39m","timestamp":"2025-08-01 01:29:20:2920"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket RSIxL1h75U1aZQSbAAAm, 原因: transport close\u001b[39m","timestamp":"2025-08-01 01:30:18:3018"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 01:31:15:3115"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 01:32:07:327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 01:32:08:328","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 01:32:08:328"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 01:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket 9pDSaWBBV2TdtL0_AAAB\u001b[39m","timestamp":"2025-08-01 01:32:48:3248"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 01:32:48:3248","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 01:32:48:3248","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 01:32:48:3248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket 9pDSaWBBV2TdtL0_AAAB, 原因: transport error\u001b[39m","timestamp":"2025-08-01 01:33:08:338"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 01:33:08:338","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 01:33:08:338","连接时长":"20秒"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/health","query":{},"requestId":"248a0a51-37fc-4139-8553-ff89a7fe247c","timestamp":"2025-08-01 01:33:45:3345","url":"/health","userAgent":"axios/1.11.0"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 01:33:45:3345"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"687000μs","timestamp":"2025-08-01 01:33:45:3345","user":"609000μs"}
{"contentLength":"376","duration":"7ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"248a0a51-37fc-4139-8553-ff89a7fe247c","statusCode":200,"timestamp":"2025-08-01 01:33:45:3345","url":"/health","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:33:45 +0000] \"GET /health HTTP/1.1\" 200 376 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:33:45:3345"}
{"headers":{"content-length":"99","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"25b167ac-377d-458c-bd5f-42f00f1e5865","timestamp":"2025-08-01 01:33:45:3345","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"19ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"25b167ac-377d-458c-bd5f-42f00f1e5865","statusCode":404,"timestamp":"2025-08-01 01:33:45:3345","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:33:45 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:33:45:3345"}
{"duration":"19ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 01:33:45:3345","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"83","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"7edd6164-85bb-4ec8-912d-0d57a03b1f8d","timestamp":"2025-08-01 01:33:48:3348","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"3ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"7edd6164-85bb-4ec8-912d-0d57a03b1f8d","statusCode":404,"timestamp":"2025-08-01 01:33:48:3348","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:33:48 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:33:48:3348"}
{"duration":"2ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 01:33:48:3348","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"140","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"f2c48e00-6da4-4f09-b280-c11f6571ea9d","timestamp":"2025-08-01 01:33:51:3351","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"2ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"f2c48e00-6da4-4f09-b280-c11f6571ea9d","statusCode":404,"timestamp":"2025-08-01 01:33:51:3351","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:33:51 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:33:51:3351"}
{"duration":"2ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 01:33:51:3351","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"158","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"53b2d342-6405-4473-98da-656abe2ac442","timestamp":"2025-08-01 01:33:54:3354","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"2ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"53b2d342-6405-4473-98da-656abe2ac442","statusCode":404,"timestamp":"2025-08-01 01:33:54:3354","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:33:54 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:33:54:3354"}
{"duration":"1ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 01:33:54:3354","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"155","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"498f2f24-0b71-4b24-aa4a-28ca99ca34da","timestamp":"2025-08-01 01:33:57:3357","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"2ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"498f2f24-0b71-4b24-aa4a-28ca99ca34da","statusCode":404,"timestamp":"2025-08-01 01:33:57:3357","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [31/Jul/2025:17:33:57 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 01:33:57:3357"}
{"duration":"1ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 01:33:57:3357","userAgent":"axios/1.11.0"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 01:37:08:378","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 01:42:08:428","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 01:47:08:478","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 01:52:08:528","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 01:57:08:578","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 01:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:02:08:28","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:07:08:78","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:12:08:128","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:17:08:178","user":"625000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:22:08:228","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:22:08:228"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:27:08:278","user":"656000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:27:08:278"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:32:08:328","user":"671000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:32:08:328"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:37:08:378","user":"671000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:37:08:378"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:42:08:428","user":"671000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:42:08:428"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:47:08:478","user":"671000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:47:08:478"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:52:08:528","user":"671000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:52:08:528"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 02:57:08:578","user":"671000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 02:57:08:578"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:02:08:28","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:07:08:78","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:12:08:128","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:17:08:178","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:22:08:228","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:22:08:228"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:27:08:278","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:27:08:278"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:32:08:328","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:32:08:328"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:37:08:378","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:42:08:428","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:47:08:478","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:52:08:528","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 03:57:08:578","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 03:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 04:02:08:28","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 04:07:08:78","user":"687000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 04:12:08:128","user":"687000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"734000μs","timestamp":"2025-08-01 04:17:08:178","user":"687000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"765000μs","timestamp":"2025-08-01 04:22:08:228","user":"703000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:22:08:228"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"765000μs","timestamp":"2025-08-01 04:27:08:278","user":"703000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:27:08:278"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"765000μs","timestamp":"2025-08-01 04:32:08:328","user":"703000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:32:08:328"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"765000μs","timestamp":"2025-08-01 04:37:08:378","user":"718000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 04:42:08:428","user":"718000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 04:47:08:478","user":"734000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 04:52:08:528","user":"734000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 04:57:08:578","user":"734000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 04:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:02:08:28","user":"734000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:07:08:78","user":"781000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:12:08:128","user":"796000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:17:08:178","user":"812000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:22:08:228","user":"812000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:22:08:228"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:27:08:278","user":"812000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:27:08:278"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:32:08:328","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:32:08:328"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:37:08:378","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:42:08:428","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:47:08:478","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:52:08:528","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 05:57:08:578","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 05:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:02:08:28","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:07:08:78","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:12:08:128","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:17:08:178","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:22:08:228","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:22:08:228"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:27:08:278","user":"828000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:27:08:278"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:32:08:328","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:32:08:328"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:37:08:378","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:42:08:428","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:47:08:478","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:52:08:528","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 06:57:08:578","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 06:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:02:08:28","user":"843000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:07:08:78","user":"875000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:12:08:128","user":"875000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:17:08:178","user":"875000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:22:08:228","user":"890000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:22:08:228"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:27:08:278","user":"890000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:27:08:278"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:32:08:328","user":"890000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:32:08:328"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:37:08:378","user":"890000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:42:08:428","user":"906000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:47:08:478","user":"906000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:52:08:528","user":"921000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 07:57:08:578","user":"921000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 07:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:02:08:28","user":"921000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:07:08:78","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:12:08:128"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:12:08:128","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:12:08:128"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:17:08:178"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:17:08:178","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:17:08:178"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:22:08:228"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:22:08:228","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:22:08:228"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:27:08:278"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:27:08:278","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:27:08:278"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:32:08:328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:32:08:328","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:32:08:328"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:37:08:378"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"781000μs","timestamp":"2025-08-01 08:37:08:378","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:37:08:378"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:42:08:428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 08:42:08:428","user":"937000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:42:08:428"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 08:47:08:478","user":"953000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:47:08:478"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 08:52:08:528","user":"953000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:52:08:528"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 08:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 08:57:08:578","user":"953000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"68MB","timestamp":"2025-08-01 08:57:08:578"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:02:08:28"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:02:08:28","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:02:08:28"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:07:08:78"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:07:08:78","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:07:08:78"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:12:09:129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:12:09:129","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:12:09:129"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:17:09:179"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:17:09:179","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:17:09:179"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:22:09:229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:22:09:229","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:22:09:229"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:27:09:279"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:27:09:279","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:27:09:279"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:32:09:329"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:32:09:329","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:32:09:329"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:37:09:379","user":"968000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:37:09:379"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:42:09:429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:42:09:429","user":"984000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:42:09:429"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:47:09:479","user":"984000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:47:09:479"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:52:09:529"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:52:09:529","user":"984000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:52:09:529"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:57:09:579"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"796000μs","timestamp":"2025-08-01 09:57:09:579","user":"984000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 09:57:09:579"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 10:02:04:24","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 10:02:04:24"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 10:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket kJZ1paeMiCaOozvYAAAB\u001b[39m","timestamp":"2025-08-01 10:02:37:237"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 10:02:37:237","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 10:02:37:237","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 10:02:37:237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket kJZ1paeMiCaOozvYAAAB, 原因: transport error\u001b[39m","timestamp":"2025-08-01 10:02:45:245"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 10:02:45:245","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 10:02:45:245","连接时长":"8秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket x2Yu55U48CxmoitJAAAD\u001b[39m","timestamp":"2025-08-01 10:06:52:652"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 10:06:52:652","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 10:06:52:652","username":"Unknown"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 10:06:52:652"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket x2Yu55U48CxmoitJAAAD, 原因: transport error\u001b[39m","timestamp":"2025-08-01 10:06:59:659"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 10:06:59:659","userId":"bidirectional_test_agent"}
{"clientId":"bidirectional_test_agent","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 10:06:59:659","连接时长":"7秒"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 10:07:04:74"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"515000μs","timestamp":"2025-08-01 10:07:04:74","user":"515000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 10:07:04:74"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/health","query":{},"requestId":"ec947c0f-da35-4b91-9e4f-9038169854ef","timestamp":"2025-08-01 10:07:56:756","url":"/health","userAgent":"axios/1.11.0"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 10:07:56:756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"515000μs","timestamp":"2025-08-01 10:07:56:756","user":"515000μs"}
{"contentLength":"377","duration":"4ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"ec947c0f-da35-4b91-9e4f-9038169854ef","statusCode":200,"timestamp":"2025-08-01 10:07:56:756","url":"/health","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:02:07:56 +0000] \"GET /health HTTP/1.1\" 200 377 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 10:07:56:756"}
{"headers":{"content-length":"99","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"11b99ffc-3ca1-47a8-a545-440d74c10f62","timestamp":"2025-08-01 10:07:56:756","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"13ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"11b99ffc-3ca1-47a8-a545-440d74c10f62","statusCode":404,"timestamp":"2025-08-01 10:07:56:756","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:02:07:56 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 10:07:56:756"}
{"duration":"13ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 10:07:56:756","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"83","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"fe9185cb-4b86-4c26-a3a1-42481ee674a0","timestamp":"2025-08-01 10:07:59:759","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"1ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"fe9185cb-4b86-4c26-a3a1-42481ee674a0","statusCode":404,"timestamp":"2025-08-01 10:07:59:759","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:02:07:59 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 10:07:59:759"}
{"duration":"2ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 10:07:59:759","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"140","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"4aac8214-cf49-4ee5-9243-ae1d0c37cb14","timestamp":"2025-08-01 10:08:02:82","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"1ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"4aac8214-cf49-4ee5-9243-ae1d0c37cb14","statusCode":404,"timestamp":"2025-08-01 10:08:02:82","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:02:08:02 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 10:08:02:82"}
{"duration":"2ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 10:08:02:82","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"158","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"f9f1e64a-135d-47e1-886c-2a120806dbcc","timestamp":"2025-08-01 10:08:05:85","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"1ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"f9f1e64a-135d-47e1-886c-2a120806dbcc","statusCode":404,"timestamp":"2025-08-01 10:08:05:85","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:02:08:05 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 10:08:05:85"}
{"duration":"1ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 10:08:05:85","userAgent":"axios/1.11.0"}
{"headers":{"content-length":"155","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","originalUrl":"/api/clients/bidirectional_test_agent/test-command","query":{},"requestId":"f9928120-3c9c-46d7-ae2a-3a4acddf1514","timestamp":"2025-08-01 10:08:08:88","url":"/api/clients/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"contentLength":"58","duration":"2ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","requestId":"f9928120-3c9c-46d7-ae2a-3a4acddf1514","statusCode":404,"timestamp":"2025-08-01 10:08:08:88","url":"/bidirectional_test_agent/test-command","userAgent":"axios/1.11.0"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:02:08:08 +0000] \"POST /api/clients/bidirectional_test_agent/test-command HTTP/1.1\" 404 58 \"-\" \"axios/1.11.0\"\u001b[39m","timestamp":"2025-08-01 10:08:08:88"}
{"duration":"1ms","endpoint":"POST /:id/test-command","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 10:08:08:88","userAgent":"axios/1.11.0"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 10:12:04:124"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"531000μs","timestamp":"2025-08-01 10:12:04:124","user":"531000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 10:12:04:124"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 10:17:04:174"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"531000μs","timestamp":"2025-08-01 10:17:04:174","user":"546000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 10:17:04:174"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 10:22:04:224"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"531000μs","timestamp":"2025-08-01 10:22:04:224","user":"546000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"67MB","timestamp":"2025-08-01 10:22:04:224"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 10:29:32:2932","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 10:29:32:2932"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 10:29:32:2932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket ybpMVZ17ubspUu8xAAAB\u001b[39m","timestamp":"2025-08-01 10:30:11:3011"}
{"action":"新客户端连接","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 10:30:11:3011","userId":"test_rat_client_001"}
{"clientId":"test_rat_client_001","hostname":"Unknown","ipAddress":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 10:30:11:3011","username":"Unknown"}
{"clientId":"test_rat_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 10:30:11:3011"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket ybpMVZ17ubspUu8xAAAB, 原因: client namespace disconnect\u001b[39m","timestamp":"2025-08-01 10:30:16:3016"}
{"action":"Socket断开: client namespace disconnect","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 10:30:16:3016","userId":"test_rat_client_001"}
{"clientId":"test_rat_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: client namespace disconnect","timestamp":"2025-08-01 10:30:16:3016","连接时长":"5秒"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"60MB","timestamp":"2025-08-01 10:34:32:3432"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"60MB","timestamp":"2025-08-01 10:34:32:3432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"281000μs","timestamp":"2025-08-01 10:34:32:3432","user":"234000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:39:32:3932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"281000μs","timestamp":"2025-08-01 10:39:32:3932","user":"234000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:39:32:3932"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:44:32:4432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"281000μs","timestamp":"2025-08-01 10:44:32:4432","user":"234000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:44:32:4432"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:49:32:4932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"281000μs","timestamp":"2025-08-01 10:49:32:4932","user":"234000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:49:32:4932"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:54:32:5432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"296000μs","timestamp":"2025-08-01 10:54:32:5432","user":"250000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:54:32:5432"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:59:32:5932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"296000μs","timestamp":"2025-08-01 10:59:32:5932","user":"250000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"61MB","timestamp":"2025-08-01 10:59:32:5932"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"54MB","timestamp":"2025-08-01 11:04:32:432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"296000μs","timestamp":"2025-08-01 11:04:32:432","user":"265000μs"}
{"external":"2MB","heapTotal":"20MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"54MB","timestamp":"2025-08-01 11:04:32:432"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 11:41:05:415","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 11:41:05:415"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 11:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket oFFFIulzt2QTvgXTAAAB\u001b[39m","timestamp":"2025-08-01 11:41:36:4136"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 11:41:36:4136","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 11:41:36:4136","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 11:41:36:4136"}
{"clientId":"client_1754019699705_zfwyn1u1d","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 11:41:39:4139"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 11:41:39:4139","userId":"client_1754019699705_zfwyn1u1d"}
{"clientId":"client_1754019699705_zfwyn1u1d","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Windows 10","timestamp":"2025-08-01 11:41:39:4139","username":"test-user"}
{"clientId":"client_1754019699705_zfwyn1u1d","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 11:41:39:4139"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket oFFFIulzt2QTvgXTAAAB, 原因: transport error\u001b[39m","timestamp":"2025-08-01 11:42:01:421"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 11:42:01:421","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 11:42:01:421","连接时长":"25秒"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 11:42:01:421","userId":"client_1754019699705_zfwyn1u1d"}
{"clientId":"client_1754019699705_zfwyn1u1d","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 11:42:01:421","连接时长":"22秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket bVqrnEwvWncLlAW3AAAD\u001b[39m","timestamp":"2025-08-01 11:42:55:4255"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 11:42:55:4255","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 11:42:55:4255","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 11:42:55:4255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket bVqrnEwvWncLlAW3AAAD, 原因: transport error\u001b[39m","timestamp":"2025-08-01 11:43:20:4320"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 11:43:20:4320","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 11:43:20:4320","连接时长":"25秒"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 11:46:05:465"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 11:46:05:465"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 11:46:05:465","user":"375000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 11:51:05:515"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 11:51:05:515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 11:51:05:515","user":"375000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 11:56:05:565"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 11:56:05:565"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 11:56:05:565","user":"375000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:01:05:15"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:01:05:15","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:06:05:65"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:06:05:65"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:06:05:65","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:11:05:115"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:11:05:115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:11:05:115","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:16:05:165"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:16:05:165"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:16:05:165","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:21:05:215"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:21:05:215"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:21:05:215","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:26:05:265"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:26:05:265"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:26:05:265","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:31:05:315"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:31:05:315"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:31:05:315","user":"421000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:36:05:365"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:36:05:365"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"234000μs","timestamp":"2025-08-01 12:36:05:365","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:41:05:415"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:41:05:415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 12:41:05:415","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:46:05:465"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:46:05:465"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 12:46:05:465","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:51:05:515"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:51:05:515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 12:51:05:515","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:56:05:565"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 12:56:05:565","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 12:56:05:565"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 13:01:05:15","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:01:05:15"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:06:05:65"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 13:06:05:65","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:06:05:65"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:11:05:115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 13:11:05:115","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:11:05:115"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:16:05:165"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 13:16:05:165","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:16:05:165"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:21:05:215"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"250000μs","timestamp":"2025-08-01 13:21:05:215","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:21:05:215"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 13:25:19:2519","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 13:25:19:2519"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 13:25:19:2519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket 7r5lJT7UCpjLGL5DAAAB\u001b[39m","timestamp":"2025-08-01 13:25:52:2552"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:25:52:2552","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:25:52:2552","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:25:52:2552"}
{"clientId":"client_1754025955156_cquchi8d9","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew client registered\u001b[39m","timestamp":"2025-08-01 13:25:55:2555"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:25:55:2555","userId":"client_1754025955156_cquchi8d9"}
{"clientId":"client_1754025955156_cquchi8d9","hostname":"test-client","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Windows 10","timestamp":"2025-08-01 13:25:55:2555","username":"test-user"}
{"clientId":"client_1754025955156_cquchi8d9","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:25:55:2555"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket 7r5lJT7UCpjLGL5DAAAB, 原因: transport error\u001b[39m","timestamp":"2025-08-01 13:26:17:2617"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:26:17:2617","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 13:26:17:2617","连接时长":"25秒"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:26:17:2617","userId":"client_1754025955156_cquchi8d9"}
{"clientId":"client_1754025955156_cquchi8d9","hostname":"test-client","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 13:26:17:2617","连接时长":"22秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket MgSenia4tOROgjMhAAAD\u001b[39m","timestamp":"2025-08-01 13:30:18:3018"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:30:18:3018","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:30:18:3018","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:30:18:3018"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"16MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:30:19:3019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"171000μs","timestamp":"2025-08-01 13:30:19:3019","user":"390000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:30:19:3019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket MgSenia4tOROgjMhAAAD, 原因: transport close\u001b[39m","timestamp":"2025-08-01 13:30:30:3030"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:30:30:3030","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 13:30:30:3030","连接时长":"11秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket y3767kdWZIOxVjpUAAAF\u001b[39m","timestamp":"2025-08-01 13:30:37:3037"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:30:37:3037","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:30:37:3037","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:30:37:3037"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket y3767kdWZIOxVjpUAAAF, 原因: transport close\u001b[39m","timestamp":"2025-08-01 13:30:44:3044"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:30:44:3044","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 13:30:44:3044","连接时长":"7秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket NckyFN_Nfe7NZAsMAAAH\u001b[39m","timestamp":"2025-08-01 13:31:29:3129"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:31:29:3129","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:31:29:3129","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:31:29:3129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket NckyFN_Nfe7NZAsMAAAH, 原因: transport error\u001b[39m","timestamp":"2025-08-01 13:31:54:3154"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:31:54:3154","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 13:31:54:3154","连接时长":"25秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket tKFXvCqK2WX50dDZAAAJ\u001b[39m","timestamp":"2025-08-01 13:35:00:350"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:35:00:350","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:35:00:350","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:35:00:350"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:35:19:3519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"203000μs","timestamp":"2025-08-01 13:35:19:3519","user":"406000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:35:19:3519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket tKFXvCqK2WX50dDZAAAJ, 原因: transport error\u001b[39m","timestamp":"2025-08-01 13:35:20:3520"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:35:20:3520","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 13:35:20:3520","连接时长":"20秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket GKkCtqKGg9c_-95cAAAL\u001b[39m","timestamp":"2025-08-01 13:37:33:3733"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:37:33:3733","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:37:33:3733","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:37:33:3733"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket GKkCtqKGg9c_-95cAAAL, 原因: transport error\u001b[39m","timestamp":"2025-08-01 13:37:53:3753"}
{"action":"Socket断开: transport error","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:37:53:3753","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport error","timestamp":"2025-08-01 13:37:53:3753","连接时长":"20秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket fStm4jBF7a4IK3JnAAAN\u001b[39m","timestamp":"2025-08-01 13:40:17:4017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket fStm4jBF7a4IK3JnAAAN, 原因: transport error\u001b[39m","timestamp":"2025-08-01 13:40:17:4017"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 13:40:19:4019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"218000μs","timestamp":"2025-08-01 13:40:19:4019","user":"406000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:40:19:4019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket eE5I_Wf-X_P-C1r1AAAP\u001b[39m","timestamp":"2025-08-01 13:41:35:4135"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket eE5I_Wf-X_P-C1r1AAAP, 原因: transport error\u001b[39m","timestamp":"2025-08-01 13:41:35:4135"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:45:19:4519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"218000μs","timestamp":"2025-08-01 13:45:19:4519","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:45:19:4519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket 0kkYX5Fq6cs01fXNAAAV\u001b[39m","timestamp":"2025-08-01 13:48:14:4814"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket 0kkYX5Fq6cs01fXNAAAV, 原因: ping timeout\u001b[39m","timestamp":"2025-08-01 13:49:39:4939"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:50:19:5019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"218000μs","timestamp":"2025-08-01 13:50:19:5019","user":"437000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:50:19:5019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket X817sQWMelR2HRn8AAAZ\u001b[39m","timestamp":"2025-08-01 13:53:45:5345"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:55:19:5519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"218000μs","timestamp":"2025-08-01 13:55:19:5519","user":"453000μs"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 13:55:19:5519"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket X817sQWMelR2HRn8AAAZ, 原因: transport close\u001b[39m","timestamp":"2025-08-01 13:55:50:5550"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket oM1sv6AMeXZ7ItW9AAAb\u001b[39m","timestamp":"2025-08-01 13:56:31:5631"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:56:31:5631","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:56:31:5631","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:56:31:5631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket oM1sv6AMeXZ7ItW9AAAb, 原因: transport close\u001b[39m","timestamp":"2025-08-01 13:57:23:5723"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 13:57:23:5723","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 13:57:23:5723","连接时长":"53秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket XytCUpzBm-vqzmFLAAAd\u001b[39m","timestamp":"2025-08-01 13:58:17:5817"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 13:58:18:5818","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 13:58:18:5818","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 13:58:18:5818"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"55MB","timestamp":"2025-08-01 14:00:19:019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"218000μs","timestamp":"2025-08-01 14:00:19:019","user":"453000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 14:00:19:019"}
{"external":"2MB","heapTotal":"19MB","heapUsed":"17MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"55MB","timestamp":"2025-08-01 14:00:19:019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m收到SIGINT信号，正在优雅关闭...\u001b[39m","timestamp":"2025-08-01 14:00:35:035"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 14:01:05:15","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 14:01:05:15"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 14:01:05:15"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/","query":{},"requestId":"83c13b16-64db-4751-922c-1d291a0f16ce","timestamp":"2025-08-01 14:01:18:118","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"161","duration":"7ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"83c13b16-64db-4751-922c-1d291a0f16ce","statusCode":200,"timestamp":"2025-08-01 14:01:18:118","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:01:18 +0000] \"GET / HTTP/1.1\" 200 161 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:01:18:118"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/favicon.ico","query":{},"requestId":"fcab829b-7914-42cc-a121-a28b6c098c58","timestamp":"2025-08-01 14:01:18:118","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m路由未找到:\u001b[39m","method":"GET","timestamp":"2025-08-01 14:01:18:118","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"135","duration":"3ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"fcab829b-7914-42cc-a121-a28b6c098c58","statusCode":404,"timestamp":"2025-08-01 14:01:18:118","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:01:18 +0000] \"GET /favicon.ico HTTP/1.1\" 404 135 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:01:18:118"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/login","query":{},"requestId":"e1647b93-b3c0-4e57-8880-1cb9d6891a6e","timestamp":"2025-08-01 14:01:29:129","url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m路由未找到:\u001b[39m","method":"GET","timestamp":"2025-08-01 14:01:29:129","url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"123","duration":"1ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"e1647b93-b3c0-4e57-8880-1cb9d6891a6e","statusCode":404,"timestamp":"2025-08-01 14:01:29:129","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:01:29 +0000] \"GET /login HTTP/1.1\" 404 123 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:01:29:129"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/api/docs","query":{},"requestId":"f04cee2f-72cc-4ded-ac28-a849868dca31","timestamp":"2025-08-01 14:01:39:139","url":"/api/docs","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m路由未找到:\u001b[39m","method":"GET","timestamp":"2025-08-01 14:01:39:139","url":"/api/docs","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"129","duration":"6ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"f04cee2f-72cc-4ded-ac28-a849868dca31","statusCode":404,"timestamp":"2025-08-01 14:01:39:139","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:01:39 +0000] \"GET /api/docs HTTP/1.1\" 404 129 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:01:39:139"}
{"duration":"6ms","endpoint":"GET /","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 14:01:39:139","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/api/auth/login","query":{},"requestId":"994170f1-8a92-4d81-b144-aa01da24fa19","timestamp":"2025-08-01 14:02:20:220","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m路由未找到:\u001b[39m","method":"GET","timestamp":"2025-08-01 14:02:20:220","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"141","duration":"3ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"994170f1-8a92-4d81-b144-aa01da24fa19","statusCode":404,"timestamp":"2025-08-01 14:02:20:220","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:02:20 +0000] \"GET /api/auth/login HTTP/1.1\" 404 141 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:02:20:220"}
{"duration":"3ms","endpoint":"GET /","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":404,"timestamp":"2025-08-01 14:02:20:220","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"headers":{"content-length":"42","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","origin":"http://localhost:3000","originalUrl":"/api/auth/login","query":{},"requestId":"b79c064d-fc6b-4d5f-9262-1ef8aea430dc","timestamp":"2025-08-01 14:02:31:231","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ipAddress":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in\u001b[39m","sessionId":"a840fa83248dac62381859e601c68de693f96f49b927967065524276cd7fd132","timestamp":"2025-08-01 14:02:31:231","userId":1,"username":"admin"}
{"duration":"217ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","origin":"http://localhost:3000","requestId":"b79c064d-fc6b-4d5f-9262-1ef8aea430dc","statusCode":200,"timestamp":"2025-08-01 14:02:31:231","url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:02:31 +0000] \"POST /api/auth/login HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:02:31:231"}
{"duration":"218ms","endpoint":"POST /login","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":200,"timestamp":"2025-08-01 14:02:31:231","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"headers":{"authorization":"[REDACTED]","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/api/clients","query":{},"requestId":"70b633af-fa51-4488-a56d-b0125acee882","timestamp":"2025-08-01 14:02:48:248","url":"/api/clients","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"action":"用户1查询客户端列表","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","method":"GET","resource":"获取客户端列表","timestamp":"2025-08-01 14:02:48:248","url":"/"}
{"duration":"2ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] API响应时间\u001b[39m","method":"GET","timestamp":"2025-08-01 14:02:48:248","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端列表查询成功\u001b[39m","timestamp":"2025-08-01 14:02:48:248","userId":1,"在线数量":0,"查询参数":{"limit":20,"page":1},"结果数量":8}
{"duration":"19ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"70b633af-fa51-4488-a56d-b0125acee882","statusCode":200,"timestamp":"2025-08-01 14:02:48:248","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:02:48 +0000] \"GET /api/clients HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:02:48:248"}
{"duration":"19ms","endpoint":"GET /","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":200,"timestamp":"2025-08-01 14:02:48:248","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket XHLpLqgN-p1kNgvqAAAC\u001b[39m","timestamp":"2025-08-01 14:03:01:31"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 14:03:01:31","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 14:03:01:31","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 14:03:01:31"}
{"headers":{"authorization":"[REDACTED]","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/api/clients","query":{},"requestId":"e3bb5cad-ec0d-4922-9b98-40e701753ae7","timestamp":"2025-08-01 14:05:27:527","url":"/api/clients","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"action":"用户1查询客户端列表","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","method":"GET","resource":"获取客户端列表","timestamp":"2025-08-01 14:05:27:527","url":"/"}
{"duration":"1ms","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] API响应时间\u001b[39m","method":"GET","timestamp":"2025-08-01 14:05:27:527","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端列表查询成功\u001b[39m","timestamp":"2025-08-01 14:05:27:527","userId":1,"在线数量":1,"查询参数":{"limit":20,"page":1},"结果数量":8}
{"duration":"5ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"e3bb5cad-ec0d-4922-9b98-40e701753ae7","statusCode":200,"timestamp":"2025-08-01 14:05:27:527","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:05:27 +0000] \"GET /api/clients HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:05:27:527"}
{"duration":"5ms","endpoint":"GET /","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":200,"timestamp":"2025-08-01 14:05:27:527","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"headers":{"authorization":"[REDACTED]","content-length":"78","content-type":"application/json"},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"POST","origin":"http://localhost:3000","originalUrl":"/api/clients/agent_client_001/commands","query":{},"requestId":"5a2db2c4-5eed-4c7a-8663-81861ec9242b","timestamp":"2025-08-01 14:05:44:544","url":"/api/clients/agent_client_001/commands","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"action":"用户1向客户端agent_client_001发送system_info命令","ip":"::1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","method":"POST","resource":"执行客户端命令","timestamp":"2025-08-01 14:05:44:544","url":"/agent_client_001/commands","userId":"agent_client_001"}
{"clientId":"agent_client_001","commandId":"cmd_1754028344396_gv1fsqc6k","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端命令发送成功\u001b[39m","timestamp":"2025-08-01 14:05:44:544","type":"system_info","userId":1}
{"contentLength":"327","duration":"5ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"POST","origin":"http://localhost:3000","requestId":"5a2db2c4-5eed-4c7a-8663-81861ec9242b","statusCode":200,"timestamp":"2025-08-01 14:05:44:544","url":"/agent_client_001/commands","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:05:44 +0000] \"POST /api/clients/agent_client_001/commands HTTP/1.1\" 200 327 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:05:44:544"}
{"duration":"6ms","endpoint":"POST /:id/commands","ip":"::1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI统计\u001b[39m","statusCode":200,"timestamp":"2025-08-01 14:05:44:544","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"external":"2MB","heapTotal":"24MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"68MB","timestamp":"2025-08-01 14:06:05:65"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"625000μs","timestamp":"2025-08-01 14:06:05:65","user":"593000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 14:06:05:65"}
{"external":"2MB","heapTotal":"24MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"68MB","timestamp":"2025-08-01 14:06:05:65"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket XHLpLqgN-p1kNgvqAAAC, 原因: transport close\u001b[39m","timestamp":"2025-08-01 14:08:29:829"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 14:08:29:829","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 14:08:29:829","连接时长":"328秒"}
{"external":"2MB","heapTotal":"24MB","heapUsed":"21MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"68MB","timestamp":"2025-08-01 14:11:05:115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"625000μs","timestamp":"2025-08-01 14:11:05:115","user":"656000μs"}
{"external":"2MB","heapTotal":"24MB","heapUsed":"21MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"68MB","timestamp":"2025-08-01 14:11:05:115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket CVYaN5mz49VJ8I-7AAAE\u001b[39m","timestamp":"2025-08-01 14:14:34:1434"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 14:14:34:1434","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 14:14:34:1434","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 14:14:34:1434"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket CVYaN5mz49VJ8I-7AAAE, 原因: transport close\u001b[39m","timestamp":"2025-08-01 14:14:49:1449"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 14:14:49:1449","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 14:14:49:1449","连接时长":"15秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m收到SIGINT信号，正在优雅关闭...\u001b[39m","timestamp":"2025-08-01 14:15:19:1519"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 14:17:32:1732","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 14:17:32:1732"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 14:17:32:1732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket A0_YuSg3WtdonaEpAAAB\u001b[39m","timestamp":"2025-08-01 14:17:49:1749"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 14:17:49:1749","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 14:17:49:1749","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 14:17:49:1749"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket A0_YuSg3WtdonaEpAAAB, 原因: transport close\u001b[39m","timestamp":"2025-08-01 14:20:23:2023"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 14:20:23:2023","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 14:20:23:2023","连接时长":"154秒"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"64MB","timestamp":"2025-08-01 14:22:32:2232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"484000μs","timestamp":"2025-08-01 14:22:32:2232","user":"421000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:22:32:2232"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:27:32:2732"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"484000μs","timestamp":"2025-08-01 14:27:32:2732","user":"421000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:27:32:2732"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:32:32:3232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"484000μs","timestamp":"2025-08-01 14:32:32:3232","user":"453000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:32:32:3232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket ZmaKUVGNJVGsLs03AAAD\u001b[39m","timestamp":"2025-08-01 14:33:05:335"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 14:33:05:335","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 14:33:05:335","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 14:33:05:335"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 14:33:35:3335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 14:33:35:3335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 14:33:36:3336"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 14:34:44:3444","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 14:34:44:3444"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 14:34:44:3444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket pbvTJaVLjb0B4P5BAAAC\u001b[39m","timestamp":"2025-08-01 14:35:17:3517"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 14:35:17:3517","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 14:35:17:3517","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 14:35:17:3517"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket pbvTJaVLjb0B4P5BAAAC, 原因: transport close\u001b[39m","timestamp":"2025-08-01 14:37:55:3755"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 14:37:55:3755","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 14:37:55:3755","连接时长":"159秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket 5D98CUSvWMqMM5vsAAAE\u001b[39m","timestamp":"2025-08-01 14:38:00:380"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 14:38:00:380","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 14:38:00:380","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 14:38:00:380"}
{"headers":{},"ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求开始\u001b[39m","method":"GET","originalUrl":"/","query":{},"requestId":"36994052-2356-46d7-ad3a-92452f4e1bff","timestamp":"2025-08-01 14:38:42:3842","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"contentLength":"161","duration":"5ms","ip":"::1","level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m请求完成\u001b[39m","method":"GET","requestId":"36994052-2356-46d7-ad3a-92452f4e1bff","statusCode":200,"timestamp":"2025-08-01 14:38:42:3842","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[35mhttp\u001b[39m","message":"\u001b[35m::1 - - [01/Aug/2025:06:38:42 +0000] \"GET / HTTP/1.1\" 200 161 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-01 14:38:42:3842"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:39:44:3944"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"546000μs","timestamp":"2025-08-01 14:39:44:3944","user":"609000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 14:39:44:3944"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:39:44:3944"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 14:44:44:4444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"546000μs","timestamp":"2025-08-01 14:44:44:4444","user":"609000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 14:44:44:4444"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 14:44:44:4444"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 14:49:44:4944"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"546000μs","timestamp":"2025-08-01 14:49:44:4944","user":"640000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 14:49:44:4944"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 14:49:44:4944"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket 5D98CUSvWMqMM5vsAAAE, 原因: transport close\u001b[39m","timestamp":"2025-08-01 14:50:20:5020"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 14:50:20:5020","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 14:50:20:5020","连接时长":"740秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m收到SIGINT信号，正在优雅关闭...\u001b[39m","timestamp":"2025-08-01 14:54:15:5415"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 14:54:44:5444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 14:54:44:5444"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 14:54:45:5445","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 14:54:45:5445"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 14:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket PpTKCHHS7lmA7yp0AAAH\u001b[39m","timestamp":"2025-08-01 14:58:09:589"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket PpTKCHHS7lmA7yp0AAAH, 原因: transport error\u001b[39m","timestamp":"2025-08-01 14:58:09:589"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:59:45:5945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"468000μs","timestamp":"2025-08-01 14:59:45:5945","user":"562000μs"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 14:59:45:5945"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 15:04:45:445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"468000μs","timestamp":"2025-08-01 15:04:45:445","user":"578000μs"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 15:04:45:445"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 15:09:45:945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"468000μs","timestamp":"2025-08-01 15:09:45:945","user":"578000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 15:09:45:945"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 15:14:45:1445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"468000μs","timestamp":"2025-08-01 15:14:45:1445","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"65MB","timestamp":"2025-08-01 15:14:45:1445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket iYWEN2N-hYjR3KJZAAAR\u001b[39m","timestamp":"2025-08-01 15:18:37:1837"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 15:18:37:1837","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 15:18:37:1837","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 15:18:37:1837"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket iYWEN2N-hYjR3KJZAAAR, 原因: transport close\u001b[39m","timestamp":"2025-08-01 15:18:49:1849"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 15:18:49:1849","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 15:18:49:1849","连接时长":"12秒"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 15:19:45:1945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"468000μs","timestamp":"2025-08-01 15:19:45:1945","user":"640000μs"}
{"external":"2MB","heapTotal":"23MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 15:19:45:1945"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 15:24:45:2445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"484000μs","timestamp":"2025-08-01 15:24:45:2445","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"66MB","timestamp":"2025-08-01 15:24:45:2445"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"57MB","timestamp":"2025-08-01 15:29:45:2945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"484000μs","timestamp":"2025-08-01 15:29:45:2945","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"57MB","timestamp":"2025-08-01 15:29:45:2945"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"53MB","timestamp":"2025-08-01 15:34:45:3445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"484000μs","timestamp":"2025-08-01 15:34:45:3445","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"53MB","timestamp":"2025-08-01 15:34:45:3445"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"52MB","timestamp":"2025-08-01 15:39:45:3945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 15:39:45:3945","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"52MB","timestamp":"2025-08-01 15:39:45:3945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket ECmhYFdqTkYIpU0SAAAT\u001b[39m","timestamp":"2025-08-01 15:43:42:4342"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 15:43:42:4342","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 15:43:42:4342","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 15:43:42:4342"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket ECmhYFdqTkYIpU0SAAAT, 原因: transport close\u001b[39m","timestamp":"2025-08-01 15:43:47:4347"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 15:43:47:4347","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 15:43:47:4347","连接时长":"5秒"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"50MB","timestamp":"2025-08-01 15:44:45:4445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 15:44:45:4445","user":"640000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"50MB","timestamp":"2025-08-01 15:44:45:4445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket KAlQu0dhLExhppMqAAAV\u001b[39m","timestamp":"2025-08-01 15:48:12:4812"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 15:48:12:4812","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 15:48:12:4812","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 15:48:12:4812"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket KAlQu0dhLExhppMqAAAV, 原因: transport close\u001b[39m","timestamp":"2025-08-01 15:48:20:4820"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 15:48:20:4820","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 15:48:20:4820","连接时长":"8秒"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"48MB","timestamp":"2025-08-01 15:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 15:49:45:4945","user":"671000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"48MB","timestamp":"2025-08-01 15:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket WGI98SsCTcCIjUi3AAAX\u001b[39m","timestamp":"2025-08-01 15:50:47:5047"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 15:50:47:5047","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 15:50:47:5047","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 15:50:47:5047"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket WGI98SsCTcCIjUi3AAAX, 原因: transport close\u001b[39m","timestamp":"2025-08-01 15:54:15:5415"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 15:54:15:5415","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 15:54:15:5415","连接时长":"207秒"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"48MB","timestamp":"2025-08-01 15:54:45:5445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 15:54:45:5445","user":"718000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"48MB","timestamp":"2025-08-01 15:54:45:5445"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 15:59:45:5945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 15:59:45:5945","user":"750000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 15:59:45:5945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket KtDkJPeXskUUANtdAAAZ\u001b[39m","timestamp":"2025-08-01 16:02:04:24"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 16:02:04:24","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 16:02:04:24","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 16:02:04:24"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket KtDkJPeXskUUANtdAAAZ, 原因: transport close\u001b[39m","timestamp":"2025-08-01 16:03:44:344"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 16:03:44:344","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 16:03:44:344","连接时长":"100秒"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"48MB","timestamp":"2025-08-01 16:04:45:445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 16:04:45:445","user":"750000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"48MB","timestamp":"2025-08-01 16:04:45:445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket Sn9DFA0a7U3VyEKZAAAb\u001b[39m","timestamp":"2025-08-01 16:08:11:811"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 16:08:11:811","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 16:08:11:811","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 16:08:11:811"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 16:09:45:945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 16:09:45:945","user":"750000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 16:09:45:945"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 16:09:45:945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket Sn9DFA0a7U3VyEKZAAAb, 原因: transport close\u001b[39m","timestamp":"2025-08-01 16:11:37:1137"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 16:11:37:1137","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 16:11:37:1137","连接时长":"206秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket sGCoGQ4P8jDbTFCoAAAd\u001b[39m","timestamp":"2025-08-01 16:11:47:1147"}
{"action":"新客户端连接","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端注册","timestamp":"2025-08-01 16:11:47:1147","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","ipAddress":"::ffff:127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端注册成功\u001b[39m","osVersion":"Unknown","timestamp":"2025-08-01 16:11:47:1147","username":"Unknown"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端注册成功\u001b[39m","timestamp":"2025-08-01 16:11:47:1147"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 16:14:45:1445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 16:14:45:1445","user":"796000μs"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMarked 1 clients as offline\u001b[39m","timestamp":"2025-08-01 16:14:45:1445"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 16:14:45:1445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket sGCoGQ4P8jDbTFCoAAAd, 原因: transport close\u001b[39m","timestamp":"2025-08-01 16:15:19:1519"}
{"action":"Socket断开: transport close","ip":"::ffff:127.0.0.1","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m[SECURITY] 数据访问\u001b[39m","resource":"客户端断开","timestamp":"2025-08-01 16:15:19:1519","userId":"agent_client_001"}
{"clientId":"agent_client_001","hostname":"Unknown","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端断开连接\u001b[39m","reason":"Socket断开: transport close","timestamp":"2025-08-01 16:15:19:1519","连接时长":"212秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket fE2J-ug0RFEVIhNHAAAf\u001b[39m","timestamp":"2025-08-01 16:15:25:1525"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket fE2J-ug0RFEVIhNHAAAf, 原因: forced close\u001b[39m","timestamp":"2025-08-01 16:15:25:1525"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket dYuoJlRSQRiYNSDZAAAh\u001b[39m","timestamp":"2025-08-01 16:15:39:1539"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket dYuoJlRSQRiYNSDZAAAh, 原因: forced close\u001b[39m","timestamp":"2025-08-01 16:15:39:1539"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 16:19:47:1947"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"500000μs","timestamp":"2025-08-01 16:19:47:1947","user":"796000μs"}
{"external":"2MB","heapTotal":"22MB","heapUsed":"20MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"47MB","timestamp":"2025-08-01 16:19:47:1947"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:20:06:206"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:20:35:2035"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:21:04:214"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:21:32:2132"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:21:59:2159"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:22:27:2227"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:22:55:2255"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:23:06:236"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 16:23:24:2324","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 16:23:24:2324"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 16:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端连接: Socket MfyCPo2AlIF57L4kAAAB\u001b[39m","timestamp":"2025-08-01 16:23:40:2340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRAT客户端断开连接: Socket MfyCPo2AlIF57L4kAAAB, 原因: forced close\u001b[39m","timestamp":"2025-08-01 16:23:40:2340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m收到SIGINT信号，正在优雅关闭...\u001b[39m","timestamp":"2025-08-01 16:27:18:2718"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 16:27:22:2722","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 16:27:22:2722"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 16:27:22:2722"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"18MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"62MB","timestamp":"2025-08-01 16:32:22:3222"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 16:32:22:3222","user":"375000μs"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 16:32:22:3222"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 16:37:22:3722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] CPU使用情况\u001b[39m","system":"703000μs","timestamp":"2025-08-01 16:37:22:3722","user":"390000μs"}
{"external":"2MB","heapTotal":"21MB","heapUsed":"19MB","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m[PERFORMANCE] 内存使用情况\u001b[39m","rss":"63MB","timestamp":"2025-08-01 16:37:22:3722"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  Default JWT secret is being used! Please change it for security.\u001b[39m","timestamp":"2025-08-01 16:41:47:4147"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m消息管道服务已初始化\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在启动Xeno-RAT服务器...\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化数据库...\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing database...\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"cache":"64MB","filename":"C:\\Users\\<USER>\\Downloads\\xeno-rat-main (1)\\xeno-rat-server\\data\\xeno-rat.db","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSQLite database initialized successfully\u001b[39m","mode":"WAL","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll migrations completed successfully\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m数据库初始化完成\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化客户端连接服务...\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m心跳监控已启动\u001b[39m","timestamp":"2025-08-01 16:41:48:4148","超时":"90秒","间隔":"30秒"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m清理任务已启动，每5分钟执行一次\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务已初始化\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m客户端连接服务初始化完成\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道架构已自动配置消息处理器\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m正在初始化Socket.IO...\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m开始初始化管道配置\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"description":"处理系统信息请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: system_info\u001b[39m","steps":["authentication","rateLimit","systemInfo"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"处理命令执行请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_execute\u001b[39m","steps":["authentication","authorization","rateLimit","routeToClient"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"处理命令执行结果","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: command_result\u001b[39m","steps":["authentication","commandResult"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"处理文件列表请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_list\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"处理文件上传请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_upload\u001b[39m","steps":["authentication","authorization","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"处理文件下载请求","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: file_download\u001b[39m","steps":["authentication","rateLimit","fileOperation"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"开始屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_start\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"停止屏幕监控","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: screen_stop\u001b[39m","steps":["authentication","authorization","rateLimit","screenMonitor"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"获取进程列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: process_list\u001b[39m","steps":["authentication","rateLimit","processManagement"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"读取注册表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: registry_read\u001b[39m","steps":["authentication","authorization","rateLimit","registryOperation"],"timestamp":"2025-08-01 16:41:48:4148"}
{"description":"获取服务列表","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m管道已注册: service_list\u001b[39m","steps":["authentication","rateLimit","serviceManagement"],"timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m已注册 11 个管道配置\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO服务已初始化\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSocket.IO初始化完成\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Xeno-RAT服务器运行在端口 3000\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔒 安全: Helmet, CORS, 频率限制已启用\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📝 日志: Winston日志系统已启用\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 数据库: SQLite WAL模式已启用\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔌 Socket.IO: 实时通信已启用\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m⏱️  请求超时: 30秒\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📈 性能监控: 已启用\u001b[39m","timestamp":"2025-08-01 16:41:48:4148"}
