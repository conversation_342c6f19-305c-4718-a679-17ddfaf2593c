﻿  Building Custom Rule C:/Users/<USER>/Downloads/xeno-rat-main (1)/rat-agent/CMakeLists.txt
cl : 命令行  warning D9002: 忽略未知选项“-g”
cl : 命令行  warning D9002: 忽略未知选项“-O0”
  test_simple_dispatcher.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\messaging\MessageDispatcher.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../test_simple_dispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\NetworkManager.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../test_simple_dispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../test_simple_dispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\messaging\MessageTypes.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../test_simple_dispatcher.cpp”)
  
  MessageDispatcher.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\messaging\MessageDispatcher.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\messaging\MessageDispatcher.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/messaging/MessageDispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\NetworkManager.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/messaging/MessageDispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/messaging/MessageDispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/messaging/MessageDispatcher.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267: “参数”: 从“size_t”转换到“_Elem”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:             _Elem=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         ]
  (编译源文件“../src/messaging/MessageDispatcher.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,16):
          查看对正在编译的 类 模板 实例化“ObfuscatedString<46>”的引用
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(23,17):
          在编译 类 模板 成员函数“std::string ObfuscatedString<46>::decrypt(void) const”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::decrypt" 的第一个引用
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267: “=”: 从“size_t”转换到“_Ty”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:             _Ty=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         ]
  (编译源文件“../src/messaging/MessageDispatcher.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(15,5):
          在编译 类 模板 成员函数“ObfuscatedString<46>::ObfuscatedString(const char *)”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::ObfuscatedString" 的第一个引用
  
  MessageTypes.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\messaging\MessageTypes.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\messaging\MessageTypes.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/messaging/MessageTypes.cpp”)
  
  NetworkManager.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\NetworkManager.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\NetworkManager.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/network/NetworkManager.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/network/NetworkManager.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/network/NetworkManager.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267: “参数”: 从“size_t”转换到“_Elem”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:             _Elem=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         ]
  (编译源文件“../src/network/NetworkManager.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,16):
          查看对正在编译的 类 模板 实例化“ObfuscatedString<46>”的引用
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(23,17):
          在编译 类 模板 成员函数“std::string ObfuscatedString<46>::decrypt(void) const”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::decrypt" 的第一个引用
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267: “=”: 从“size_t”转换到“_Ty”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:             _Ty=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         ]
  (编译源文件“../src/network/NetworkManager.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(15,5):
          在编译 类 模板 成员函数“ObfuscatedString<46>::ObfuscatedString(const char *)”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::ObfuscatedString" 的第一个引用
  
  WebSocketClient.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\network\WebSocketClient.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/network/WebSocketClient.cpp”)
  
  easywsclient.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(103,42): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(205,27): warning C4244: “参数”: 从“socket_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(209,19): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(230,66): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(497,88): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(499,84): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(502,93): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(504,84): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(505,85): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(507,96): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(509,109): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(510,91): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\easywsclient-master\easywsclient.cpp(511,66): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
  Logger.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\utils\Logger.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\utils\Logger.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/utils/Logger.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/utils/Logger.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\utils\Logger.cpp(216,31): warning C4996: 'localtime': This function or variable may be unsafe. Consider using localtime_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267: “参数”: 从“size_t”转换到“_Elem”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:             _Elem=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         ]
  (编译源文件“../src/utils/Logger.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,16):
          查看对正在编译的 类 模板 实例化“ObfuscatedString<46>”的引用
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(23,17):
          在编译 类 模板 成员函数“std::string ObfuscatedString<46>::decrypt(void) const”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::decrypt" 的第一个引用
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267: “=”: 从“size_t”转换到“_Ty”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:             _Ty=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         ]
  (编译源文件“../src/utils/Logger.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(15,5):
          在编译 类 模板 成员函数“ObfuscatedString<46>::ObfuscatedString(const char *)”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::ObfuscatedString" 的第一个引用
  
  ErrorHandler.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\utils\ErrorHandler.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\utils\ErrorHandler.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/utils/ErrorHandler.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\utils\Logger.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/utils/ErrorHandler.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/utils/ErrorHandler.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267: “参数”: 从“size_t”转换到“_Elem”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:             _Elem=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         ]
  (编译源文件“../src/utils/ErrorHandler.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,16):
          查看对正在编译的 类 模板 实例化“ObfuscatedString<46>”的引用
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(23,17):
          在编译 类 模板 成员函数“std::string ObfuscatedString<46>::decrypt(void) const”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::decrypt" 的第一个引用
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267: “=”: 从“size_t”转换到“_Ty”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:             _Ty=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         ]
  (编译源文件“../src/utils/ErrorHandler.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(15,5):
          在编译 类 模板 成员函数“ObfuscatedString<46>::ObfuscatedString(const char *)”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(199,9):
              请参阅 "ObfuscatedStrings::GetMutexName" 中对 "ObfuscatedString<46>::ObfuscatedString" 的第一个引用
  
  AntiDebug.cpp
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\AntiDebug.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\AntiDebug.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/security/AntiDebug.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/security/AntiDebug.cpp”)
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267: “参数”: 从“size_t”转换到“_Elem”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:             _Elem=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32): warning C4267:         ]
  (编译源文件“../src/security/AntiDebug.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(27,32):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\AntiDebug.cpp(289,28):
          查看对正在编译的 类 模板 实例化“ObfuscatedString<26>”的引用
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(23,17):
          在编译 类 模板 成员函数“std::string ObfuscatedString<26>::decrypt(void) const”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\AntiDebug.cpp(289,26):
              请参阅 "AntiDebug::CheckMemoryPatches" 中对 "ObfuscatedString<26>::decrypt" 的第一个引用
  
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267: “=”: 从“size_t”转换到“_Ty”，可能丢失数据
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         with
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         [
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:             _Ty=char
C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30): warning C4267:         ]
  (编译源文件“../src/security/AntiDebug.cpp”)
      C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(17,30):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\Obfuscation.h(15,5):
          在编译 类 模板 成员函数“ObfuscatedString<26>::ObfuscatedString(const char *)”时
              C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\src\security\AntiDebug.cpp(289,26):
              请参阅 "AntiDebug::CheckMemoryPatches" 中对 "ObfuscatedString<26>::ObfuscatedString" 的第一个引用
  
  正在生成代码...
  SimpleDispatcherTest.vcxproj -> C:\Users\<USER>\Downloads\xeno-rat-main (1)\rat-agent\build\bin\Release\test_simple_dispatcher.exe
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
