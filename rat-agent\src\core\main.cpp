﻿#include <iostream>
#include <cstdio>

#ifdef _WIN32
#include <windows.h>
#endif

/**
 * 简化的主程序入口点
 */

#include "MinimalAgent.h"
#include "../utils/Logger.h"
#include "../security/AntiDebug.h"

int main(int argc, char* argv[]) {
    // 初始化反调试保护
    // ANTI_DEBUG_INIT();

    // 执行反调试检查
    // if (AntiDebug::PerformChecks()) {
    //     // 检测到调试器，静默退出
    //     return 0;
    // }

    try {
        // 创建并启动代理
        MinimalAgent agent;

        if (!agent.Initialize()) {
            return 1;
        }

        // 运行代理
        agent.Run();

        // 清理
        agent.Cleanup();

        return 0;
    }
    catch (...) {
        // 静默处理异常
        return 1;
    }
}
